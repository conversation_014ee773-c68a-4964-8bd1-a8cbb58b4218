// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String       @id @default(cuid())
  name          String? // Added for NextAuth
  email         String       @unique
  emailVerified DateTime? // Added for NextAuth
  image         String? // Added for NextAuth
  password      String
  passwordResetToken String? @unique
  passwordResetExpires DateTime?
  failedLoginAttempts Int @default(0)
  lockedUntil   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  profile       Profile? // One-to-one relationship with Profile
  assessments   Assessment[] // One-to-many relationship with Assessment
  forumPosts    ForumPost[] // One-to-many relationship with ForumPost
  forumReplies  ForumReply[] // One-to-many relationship with ForumReply
  freedomFund   FreedomFund? // One-to-one relationship with FreedomFund
  learningProgress UserLearningProgress[] // One-to-many relationship with UserLearningProgress
  resourceRatings  ResourceRating[] // One-to-many relationship with ResourceRating

  // Added for NextAuth
  accounts Account[]
  sessions Session[]
}

model Profile {
  id                String   @id @default(uuid())
  userId            String   @unique // One-to-one relationship with User
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  bio               String?
  profilePictureUrl String?
  socialMediaLinks  Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

enum AssessmentStatus {
  IN_PROGRESS
  COMPLETED
}

model Assessment {
  id          String               @id @default(uuid())
  userId      String
  user        User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  status      AssessmentStatus     @default(IN_PROGRESS)
  currentStep Int                  @default(0)
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  completedAt DateTime?
  responses   AssessmentResponse[]
}

model AssessmentResponse {
  id           String     @id @default(uuid())
  assessmentId String
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  questionKey  String // e.g., "dissatisfaction_triggers", "desired_outcomes_skill_a"
  answerValue  Json // Flexible to store various answer types (string, array, number)
  createdAt    DateTime   @default(now())

  @@index([assessmentId])
}

model CareerPath {
  id                String           @id @default(uuid())
  name              String           @unique
  slug              String           @unique // For URL-friendly identifiers
  overview          String
  pros              String // JSON string of advantages
  cons              String // JSON string of disadvantages
  actionableSteps   Json // e.g., [{ title: "Step 1", description: "..."}]
  isActive          Boolean          @default(true)
  relatedSkills     Skill[]          @relation("CareerPathToSkill")
  relatedIndustries Industry[]       @relation("CareerPathToIndustry")
  suggestionRules   SuggestionRule[]
  learningResources LearningResource[] @relation("CareerPathToLearningResource")
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
}

model Skill {
  id                String             @id @default(uuid())
  name              String             @unique
  description       String?
  category          String? // e.g., "Technical", "Soft Skills", "Industry-Specific"
  careerPaths       CareerPath[]       @relation("CareerPathToSkill")
  learningResources LearningResource[] @relation("SkillToLearningResource")
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
}

model Industry {
  id          String       @id @default(uuid())
  name        String       @unique
  careerPaths CareerPath[] @relation("CareerPathToIndustry")
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model SuggestionRule {
  id           String     @id @default(uuid())
  careerPathId String
  careerPath   CareerPath @relation(fields: [careerPathId], references: [id], onDelete: Cascade)
  questionKey  String // Corresponds to AssessmentResponse.questionKey
  answerValue  Json // The specific answer value or pattern that triggers this rule
  weight       Float      @default(1.0) // How much this rule contributes
  notes        String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  @@index([careerPathId])
}

model ForumPost {
  id        String       @id @default(uuid())
  title     String
  content   String
  authorId  String
  author    User         @relation(fields: [authorId], references: [id], onDelete: Cascade)
  replies   ForumReply[] // One-to-many relationship with ForumReply
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
}

model ForumReply {
  id        String    @id @default(uuid())
  content   String
  authorId  String
  author    User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  postId    String
  post      ForumPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model FreedomFund {
  id                   String   @id @default(uuid())
  userId               String   @unique
  user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  monthlyExpenses      Float
  coverageMonths       Int
  targetSavings        Float
  currentSavingsAmount Float? // Optional, as user might not have started saving
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt // Automatically updated on save
}

// Added for NextAuth
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// Added for NextAuth
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Added for NextAuth
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// New models for Learning Resources
model LearningResource {
  id                String                    @id @default(uuid())
  title             String
  description       String
  url               String                    @unique
  type              LearningResourceType
  category          LearningResourceCategory
  skillLevel        SkillLevel
  author            String?
  duration          String?
  cost              LearningResourceCost      @default(FREE)
  format            LearningResourceFormat
  isActive          Boolean                   @default(true)
  careerPaths       CareerPath[]              @relation("CareerPathToLearningResource")
  skills            Skill[]                   @relation("SkillToLearningResource")
  userProgress      UserLearningProgress[]
  ratings           ResourceRating[]
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt
}

model UserLearningProgress {
  id                String           @id @default(uuid())
  userId            String
  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  resourceId        String
  resource          LearningResource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  status            ProgressStatus   @default(NOT_STARTED)
  completedAt       DateTime?
  notes             String?
  rating            Int?             // 1-5 star rating
  review            String?          // User review text
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@unique([userId, resourceId])
}

model ResourceRating {
  id                String           @id @default(uuid())
  userId            String
  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  resourceId        String
  resource          LearningResource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  rating            Int              // 1-5 star rating
  review            String?          // Optional review text
  isHelpful         Boolean?         // Whether user found resource helpful
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@unique([userId, resourceId])
}

enum LearningResourceType {
  COURSE
  ARTICLE
  VIDEO
  PODCAST
  BOOK
  CERTIFICATION
  TUTORIAL
  WORKSHOP
}

enum LearningResourceCategory {
  CYBERSECURITY
  DATA_SCIENCE
  BLOCKCHAIN
  PROJECT_MANAGEMENT
  DIGITAL_MARKETING
  FINANCIAL_LITERACY
  LANGUAGE_LEARNING
  ARTIFICIAL_INTELLIGENCE
  WEB_DEVELOPMENT
  MOBILE_DEVELOPMENT
  CLOUD_COMPUTING
  ENTREPRENEURSHIP
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum LearningResourceCost {
  FREE
  FREEMIUM
  PAID
  SUBSCRIPTION
}

enum LearningResourceFormat {
  SELF_PACED
  INSTRUCTOR_LED
  INTERACTIVE
  HANDS_ON
  THEORETICAL
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  BOOKMARKED
}
