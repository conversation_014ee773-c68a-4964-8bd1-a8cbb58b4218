'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { BookOpen, Star, ExternalLink, Play, Bookmark, Clock, User, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LearningResource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  author?: string;
  duration?: string;
  cost: string;
  averageRating: number;
  totalRatings: number;
  careerPaths: {
    id: string;
    name: string;
    slug: string;
  }[];
}

interface PersonalizedResourcesData {
  resources: LearningResource[];
  suggestedCareerPaths: {
    id: string;
    name: string;
    slug: string;
  }[];
  interests: string[];
  recommendationReason: string;
}

export default function PersonalizedResources() {
  const { data: session } = useSession();
  const [data, setData] = useState<PersonalizedResourcesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user?.id) {
      fetchPersonalizedResources();
    }
  }, [session]);

  const fetchPersonalizedResources = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/personalized-resources?limit=6');
      
      if (!response.ok) {
        throw new Error('Failed to fetch personalized resources');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch resources');
      }
    } catch (err) {
      console.error('Error fetching personalized resources:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleResourceProgress = async (resourceId: string, status: string) => {
    try {
      const response = await fetch('/api/learning-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resourceId,
          status
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update resource progress');
      }
    } catch (error) {
      console.error('Error updating resource progress:', error);
    }
  };

  if (!session) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          Personalized Learning Resources
        </h2>
        <div className="text-center py-8">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Sign in to get personalized learning recommendations based on your assessment results.
          </p>
          <Button asChild>
            <Link href="/login">Sign In</Link>
          </Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          Personalized Learning Resources
        </h2>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          Personalized Learning Resources
        </h2>
        <div className="text-center py-8">
          <p className="text-red-600 dark:text-red-400 mb-4">
            {error || 'Failed to load personalized resources'}
          </p>
          <Button onClick={fetchPersonalizedResources} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          Recommended for You
        </h2>
        <Button asChild variant="outline" size="sm">
          <Link href="/resources">View All</Link>
        </Button>
      </div>

      <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
        {data.recommendationReason}
      </p>

      <div className="space-y-4">
        {data.resources && data.resources.length > 0 ? data.resources.slice(0, 3).map((resource) => (
          <div
            key={resource.id}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                  {resource.title}
                </h3>
                <div className="flex items-center gap-2 mb-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    resource.skillLevel === 'BEGINNER' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                    resource.skillLevel === 'INTERMEDIATE' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {resource.skillLevel.toLowerCase()}
                  </span>
                  {resource.cost === 'FREE' && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs font-medium">
                      Free
                    </span>
                  )}
                  {resource.averageRating > 0 && (
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 text-yellow-500 fill-current" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        {resource.averageRating.toFixed(1)}
                      </span>
                    </div>
                  )}
                </div>
                {resource.author && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                    by {resource.author}
                    {resource.duration && ` • ${resource.duration}`}
                  </p>
                )}
              </div>
            </div>

            <p className="text-sm text-gray-700 dark:text-gray-300 mb-3 line-clamp-2">
              {resource.description}
            </p>

            <div className="flex gap-2">
              <Button
                asChild
                size="sm"
                className="flex-1"
              >
                <a
                  href={resource.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center gap-2"
                >
                  <Play className="h-3 w-3" />
                  Start
                  <ExternalLink className="h-3 w-3" />
                </a>
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleResourceProgress(resource.id, 'BOOKMARKED')}
                className="px-3"
              >
                <Bookmark className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )) : (
          <div className="text-center py-8">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              No personalized resources available yet. Complete an assessment to get personalized recommendations.
            </p>
            <Button asChild>
              <Link href="/assessment">Take Assessment</Link>
            </Button>
          </div>
        )}
      </div>

      {data.suggestedCareerPaths && data.suggestedCareerPaths.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Based on your interest in:
          </p>
          <div className="flex flex-wrap gap-2">
            {data.suggestedCareerPaths.map((path) => (
              <Link
                key={path.id}
                href={`/career-paths/${path.id}`}
                className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-xs hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
              >
                {path.name}
              </Link>
            ))}
          </div>
        </div>
      )}

      <div className="mt-4 text-center">
        <Button asChild variant="outline" size="sm">
          <Link href="/assessment">
            Update Preferences
          </Link>
        </Button>
      </div>
    </div>
  );
}
