'use client';

import React, { useState, useEffect } from 'react';
import CookieConsent from 'react-cookie-consent';

const CookieConsentBanner = () => {
  const [analyticsDisabled, setAnalyticsDisabled] = useState(false);

  useEffect(() => {
    setAnalyticsDisabled(localStorage.getItem('va-disable') === 'true');
  }, []);

  const handleToggleAnalytics = () => {
    if (analyticsDisabled) {
      localStorage.removeItem('va-disable');
    } else {
      localStorage.setItem('va-disable', 'true');
    }
    setAnalyticsDisabled(!analyticsDisabled);
    // Reload the page to apply analytics changes
    window.location.reload();
  };

  return (
    <CookieConsent
      location="bottom"
      buttonText="I understand"
      cookieName="careerPlatformCookieConsent"
      style={{ background: "#2B373B", zIndex: 1000 }} // Added zIndex to ensure visibility
      buttonStyle={{ color: "#FFFFFF", background: "#007BFF", fontSize: "13px" }}
      expires={365}
    >
      This website uses cookies to enhance the user experience.{" "}
      <span style={{ fontSize: "10px" }}>
        Please see our <a href="/privacy-policy" className="underline hover:text-blue-300">Privacy Policy</a> for more details.
      </span>
      <div className="mt-2 text-sm text-gray-300">
        <label className="inline-flex items-center">
          <input
            type="checkbox"
            className="form-checkbox h-4 w-4 text-blue-600 rounded"
            checked={!analyticsDisabled}
            onChange={handleToggleAnalytics}
          />
          <span className="ml-2">Enable Anonymous Analytics (recommended)</span>
        </label>
      </div>
    </CookieConsent>
  );
};

export default CookieConsentBanner;
