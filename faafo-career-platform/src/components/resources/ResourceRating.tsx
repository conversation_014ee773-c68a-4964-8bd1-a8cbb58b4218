'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Star, MessageSquare, ThumbsUp, User } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Rating {
  id: string;
  rating: number;
  review?: string;
  isHelpful?: boolean;
  createdAt: string;
  user: {
    id: string;
    name?: string;
    image?: string;
  };
}

interface ResourceRatingProps {
  resourceId: string;
  resourceTitle: string;
}

export default function ResourceRating({ resourceId, resourceTitle }: ResourceRatingProps) {
  const { data: session } = useSession();
  const [ratings, setRatings] = useState<Rating[]>([]);
  const [averageRating, setAverageRating] = useState(0);
  const [totalRatings, setTotalRatings] = useState(0);
  const [userRating, setUserRating] = useState<number>(0);
  const [userReview, setUserReview] = useState<string>('');
  const [isHelpful, setIsHelpful] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);

  useEffect(() => {
    fetchRatings();
  }, [resourceId]);

  const fetchRatings = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/resource-ratings?resourceId=${resourceId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch ratings');
      }

      const result = await response.json();
      if (result.success) {
        setRatings(result.data.ratings);
        setAverageRating(result.data.averageRating);
        setTotalRatings(result.data.totalRatings);

        // Check if user has already rated this resource
        if (session?.user?.id) {
          const existingRating = result.data.ratings.find(
            (rating: Rating) => rating.user.id === session.user?.id
          );
          if (existingRating) {
            setUserRating(existingRating.rating);
            setUserReview(existingRating.review || '');
            setIsHelpful(existingRating.isHelpful || null);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching ratings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRatingSubmit = async () => {
    if (!session?.user?.id || userRating === 0) return;

    try {
      setSubmitting(true);
      const response = await fetch('/api/resource-ratings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resourceId,
          rating: userRating,
          review: userReview.trim() || undefined,
          isHelpful
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit rating');
      }

      const result = await response.json();
      if (result.success) {
        await fetchRatings(); // Refresh ratings
        setShowReviewForm(false);
      }
    } catch (error) {
      console.error('Error submitting rating:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating: number, interactive: boolean = false, onStarClick?: (star: number) => void) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => interactive && onStarClick?.(star)}
            disabled={!interactive}
            className={`${interactive ? 'hover:scale-110 transition-transform' : ''}`}
          >
            <Star
              className={`h-4 w-4 ${
                star <= rating
                  ? 'text-yellow-500 fill-current'
                  : 'text-gray-300 dark:text-gray-600'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
      <h3 className="text-lg font-semibold mb-4">Ratings & Reviews</h3>

      {/* Overall Rating */}
      <div className="flex items-center gap-4 mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {averageRating.toFixed(1)}
          </div>
          {renderStars(averageRating)}
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {totalRatings} {totalRatings === 1 ? 'review' : 'reviews'}
          </div>
        </div>
      </div>

      {/* User Rating Form */}
      {session?.user?.id && (
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="font-medium mb-3">
            {userRating > 0 ? 'Update your rating' : 'Rate this resource'}
          </h4>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Your rating</label>
              {renderStars(userRating, true, setUserRating)}
            </div>

            {userRating > 0 && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Was this resource helpful?
                  </label>
                  <div className="flex gap-2">
                    <Button
                      variant={isHelpful === true ? "default" : "outline"}
                      size="sm"
                      onClick={() => setIsHelpful(true)}
                    >
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      Yes
                    </Button>
                    <Button
                      variant={isHelpful === false ? "default" : "outline"}
                      size="sm"
                      onClick={() => setIsHelpful(false)}
                    >
                      No
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Review (optional)
                  </label>
                  <textarea
                    value={userReview}
                    onChange={(e) => setUserReview(e.target.value)}
                    placeholder="Share your thoughts about this resource..."
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                    rows={3}
                  />
                </div>

                <Button
                  onClick={handleRatingSubmit}
                  disabled={submitting}
                  className="w-full"
                >
                  {submitting ? 'Submitting...' : 'Submit Rating'}
                </Button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Reviews List */}
      {ratings.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium">Recent Reviews</h4>
          {ratings.slice(0, 5).map((rating) => (
            <div key={rating.id} className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  {rating.user.image ? (
                    <img
                      src={rating.user.image}
                      alt={rating.user.name || 'User'}
                      className="w-8 h-8 rounded-full"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      <User className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">
                      {rating.user.name || 'Anonymous'}
                    </span>
                    {renderStars(rating.rating)}
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(rating.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  {rating.review && (
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {rating.review}
                    </p>
                  )}
                  {rating.isHelpful !== null && (
                    <div className="flex items-center gap-1 mt-2">
                      <ThumbsUp className={`h-3 w-3 ${
                        rating.isHelpful ? 'text-green-600' : 'text-red-600'
                      }`} />
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        {rating.isHelpful ? 'Found helpful' : 'Not helpful'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {!session?.user?.id && (
        <div className="text-center py-4">
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            Sign in to rate and review this resource
          </p>
          <Button asChild variant="outline" size="sm">
            <a href="/login">Sign In</a>
          </Button>
        </div>
      )}
    </div>
  );
}
