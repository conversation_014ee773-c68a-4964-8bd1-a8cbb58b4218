'use client';

import React, { useState, useMemo } from 'react';
import Fuse from 'fuse.js';
import Link from 'next/link';

interface HelpDocument {
  title: string;
  content: string;
  url: string;
}

interface HelpSearchProps {
  documents: HelpDocument[];
}

const HelpSearch: React.FC<HelpSearchProps> = ({ documents }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const fuse = useMemo(() => {
    return new Fuse(documents, {
      keys: ['title', 'content'],
      includeScore: true,
      threshold: 0.3, // Adjust this value to control fuzzy matching strictness
    });
  }, [documents]);

  const searchResults = useMemo(() => {
    if (!searchTerm) {
      return [];
    }
    return fuse.search(searchTerm);
  }, [searchTerm, fuse]);

  return (
    <div className="w-full max-w-2xl mx-auto">
      <input
        type="text"
        placeholder="Search help resources..."
        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />

      {searchTerm && searchResults.length > 0 && (
        <div className="mt-4 bg-white dark:bg-gray-800 shadow-lg rounded-md max-h-96 overflow-y-auto">
          {searchResults.map((result) => (
            <Link key={result.refIndex} href={result.item.url} className="block p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{result.item.title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{result.item.content}</p>
              {result.score !== undefined && (
                <span className="text-xs text-gray-500 dark:text-gray-500">Score: {(result.score * 100).toFixed(2)}%</span>
              )}
            </Link>
          ))}
        </div>
      )}

      {searchTerm && searchResults.length === 0 && (
        <div className="mt-4 p-4 text-center text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 shadow-lg rounded-md">
          No results found for &quot;{searchTerm}&quot;.
        </div>
      )}
    </div>
  );
};

export default HelpSearch; 