import prisma from '../prisma';
import { createError } from '../errorHandler';
import type { Assessment, AssessmentResponse, AssessmentStatus } from '@prisma/client';

export interface AssessmentWithResponses extends Assessment {
  responses: AssessmentResponse[];
}

export interface CreateAssessmentData {
  userId: string;
  currentStep?: number;
  status?: AssessmentStatus;
}

export interface UpdateAssessmentData {
  currentStep?: number;
  status?: AssessmentStatus;
  formData?: Record<string, any>;
}

export class AssessmentService {
  static async createAssessment(data: CreateAssessmentData): Promise<AssessmentWithResponses> {
    const assessment = await prisma.assessment.create({
      data: {
        userId: data.userId,
        currentStep: data.currentStep || 0,
        status: data.status || 'IN_PROGRESS'
      },
      include: {
        responses: true
      }
    });

    return assessment;
  }

  static async getAssessmentById(id: string): Promise<AssessmentWithResponses | null> {
    const assessment = await prisma.assessment.findUnique({
      where: { id },
      include: {
        responses: true
      }
    });

    return assessment;
  }

  static async getUserActiveAssessment(userId: string): Promise<AssessmentWithResponses | null> {
    const assessment = await prisma.assessment.findFirst({
      where: {
        userId,
        status: 'IN_PROGRESS'
      },
      include: {
        responses: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return assessment;
  }

  static async updateAssessment(
    id: string, 
    data: UpdateAssessmentData
  ): Promise<AssessmentWithResponses> {
    const assessment = await prisma.assessment.findUnique({
      where: { id }
    });

    if (!assessment) {
      throw createError('NOT_FOUND', 'Assessment not found', 404);
    }

    const updateData: any = {};

    if (data.currentStep !== undefined) {
      updateData.currentStep = data.currentStep;
    }

    if (data.status !== undefined) {
      updateData.status = data.status;
      if (data.status === 'COMPLETED') {
        updateData.completedAt = new Date();
      }
    }

    // Update assessment
    const updatedAssessment = await prisma.assessment.update({
      where: { id },
      data: updateData,
      include: {
        responses: true
      }
    });

    // Update responses if provided
    if (data.formData) {
      await this.updateAssessmentResponses(id, data.formData);
    }

    return updatedAssessment;
  }

  static async updateAssessmentResponses(
    assessmentId: string,
    formData: Record<string, any>
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Delete existing responses
      await tx.assessmentResponse.deleteMany({
        where: { assessmentId }
      });

      // Create new responses
      const responseData = Object.entries(formData).map(([key, value]) => ({
        assessmentId,
        questionKey: key,
        answerValue: value === null || value === undefined ? null : value
      }));

      if (responseData.length > 0) {
        await tx.assessmentResponse.createMany({
          data: responseData
        });
      }
    });
  }

  static async saveAssessmentProgress(
    userId: string,
    currentStep: number,
    formData: Record<string, any>,
    status?: AssessmentStatus
  ): Promise<AssessmentWithResponses> {
    // Get or create assessment
    let assessment = await this.getUserActiveAssessment(userId);

    if (!assessment) {
      assessment = await this.createAssessment({
        userId,
        currentStep,
        status: status || 'IN_PROGRESS'
      });
    }

    // Update assessment
    const updatedAssessment = await this.updateAssessment(assessment.id, {
      currentStep,
      status: status || assessment.status,
      formData
    });

    return updatedAssessment;
  }

  static async submitAssessment(
    assessmentId: string,
    formData: Record<string, any>
  ): Promise<AssessmentWithResponses> {
    const assessment = await this.getAssessmentById(assessmentId);

    if (!assessment) {
      throw createError('NOT_FOUND', 'Assessment not found', 404);
    }

    if (assessment.status === 'COMPLETED') {
      throw createError('CONFLICT', 'Assessment is already completed', 409);
    }

    const updatedAssessment = await this.updateAssessment(assessmentId, {
      status: 'COMPLETED',
      formData
    });

    return updatedAssessment;
  }

  static async getAssessmentFormData(assessmentId: string): Promise<Record<string, any>> {
    const responses = await prisma.assessmentResponse.findMany({
      where: { assessmentId }
    });

    const formData: Record<string, any> = {};
    responses.forEach(response => {
      formData[response.questionKey] = response.answerValue;
    });

    return formData;
  }

  static async validateAssessmentOwnership(assessmentId: string, userId: string): Promise<boolean> {
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      select: { userId: true }
    });

    return assessment?.userId === userId;
  }
}
