// Define types for questions and steps
export interface Option {
  value: string;
  label: string;
}

export interface BaseQuestion {
  key: string;
  text: string;
  description?: string;
}

export interface MCQuestion extends BaseQuestion {
  type: 'multipleChoice';
  options: Option[];
  allowMultiple?: boolean;
  required?: boolean; // Added for server-side validation consistency
}

export interface ScQuestion extends BaseQuestion {
  type: 'scale';
  minLabel: string;
  maxLabel: string;
  numberOfSteps: number;
  required?: boolean; // Added for server-side validation consistency
}

export interface TextQuestion extends BaseQuestion {
  type: 'text';
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
  required?: boolean;
}

export type Question = MCQuestion | ScQuestion | TextQuestion;

export interface AssessmentStep {
  step: number;
  title: string;
  questions: Question[];
}

// --- Enhanced Assessment Definition ---
export const assessmentDefinition: AssessmentStep[] = [
  {
    step: 1,
    title: 'Understanding Your Current Situation',
    questions: [
      {
        key: 'dissatisfaction_triggers',
        text: 'What are the primary triggers for your current job dissatisfaction?',
        description: 'Select all that apply. Understanding your pain points helps us recommend the best path forward.',
        type: 'multipleChoice',
        allowMultiple: true,
        required: true,
        options: [
          { value: 'work_life_balance', label: 'Poor Work-Life Balance' },
          { value: 'lack_of_growth', label: 'Lack of Growth Opportunities' },
          { value: 'compensation', label: 'Inadequate Compensation' },
          { value: 'company_culture', label: 'Negative Company Culture' },
          { value: 'management', label: 'Issues with Management' },
          { value: 'work_meaningless', label: 'Work Feels Meaningless' },
          { value: 'burnout', label: 'Burnout and Stress' },
          { value: 'lack_autonomy', label: 'Lack of Autonomy and Control' },
          { value: 'commute', label: 'Long Commute or Location Issues' },
          { value: 'job_security', label: 'Job Security Concerns' },
          { value: 'skills_mismatch', label: 'Skills Not Being Utilized' },
          { value: 'other', label: 'Other (please specify if possible in thoughts later)' },
        ],
      },
      {
        key: 'current_employment_status',
        text: 'What is your current employment status?',
        description: 'This helps us understand your timeline and urgency.',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: 'employed_full_time', label: 'Employed Full-Time' },
          { value: 'employed_part_time', label: 'Employed Part-Time' },
          { value: 'unemployed_seeking', label: 'Unemployed and Seeking Work' },
          { value: 'unemployed_transitioning', label: 'Unemployed and Planning Career Change' },
          { value: 'self_employed', label: 'Self-Employed/Freelancing' },
          { value: 'student', label: 'Student' },
          { value: 'other', label: 'Other' },
        ],
      },
      {
        key: 'years_experience',
        text: 'How many years of professional work experience do you have?',
        description: 'Include all relevant work experience, including internships and part-time roles.',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: '0-2', label: '0-2 years (Entry Level)' },
          { value: '3-5', label: '3-5 years (Early Career)' },
          { value: '6-10', label: '6-10 years (Mid-Career)' },
          { value: '11-15', label: '11-15 years (Senior Level)' },
          { value: '16-20', label: '16-20 years (Executive Level)' },
          { value: '20+', label: '20+ years (Veteran)' },
        ],
      },
    ],
  },
  {
    step: 2,
    title: 'Defining Your Desired Outcomes',
    questions: [
      {
        key: 'financial_comfort',
        text: 'How would you rate your current financial comfort regarding a career change?',
        description: 'Consider your savings, expenses, and financial obligations. 1 = Very anxious about finances, 5 = Very comfortable financially',
        type: 'scale',
        minLabel: 'Very Anxious',
        maxLabel: 'Very Comfortable',
        numberOfSteps: 5,
        required: true,
      },
      {
        key: 'desired_outcomes_work_life',
        text: 'How important is improved work-life balance in your next role?',
        description: 'Consider factors like flexible hours, remote work, time for personal life.',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: 'not_important', label: 'Not Important' },
          { value: 'somewhat_important', label: 'Somewhat Important' },
          { value: 'very_important', label: 'Very Important' },
          { value: 'critical', label: 'Critical - This is my top priority' },
        ],
      },
      {
        key: 'desired_outcomes_compensation',
        text: 'How important is increasing your compensation in your next role?',
        description: 'Consider salary, benefits, bonuses, and overall financial package.',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: 'not_important', label: 'Not Important - I\'m willing to take a pay cut' },
          { value: 'maintain_current', label: 'Maintain Current Level' },
          { value: 'moderate_increase', label: 'Moderate Increase (10-25%)' },
          { value: 'significant_increase', label: 'Significant Increase (25%+)' },
          { value: 'critical', label: 'Critical - Major financial improvement needed' },
        ],
      },
      {
        key: 'desired_outcomes_autonomy',
        text: 'How important is having more autonomy and control over your work?',
        description: 'This includes decision-making power, creative freedom, and independence.',
        type: 'scale',
        minLabel: 'Not Important',
        maxLabel: 'Extremely Important',
        numberOfSteps: 5,
        required: true,
      },
    ],
  },
  {
    step: 3,
    title: 'Skills and Strengths Assessment',
    questions: [
      {
        key: 'top_skills',
        text: 'What are your top professional skills and strengths?',
        description: 'Select up to 5 skills that you feel most confident about and enjoy using.',
        type: 'multipleChoice',
        allowMultiple: true,
        required: true,
        options: [
          { value: 'communication', label: 'Communication & Presentation' },
          { value: 'leadership', label: 'Leadership & Team Management' },
          { value: 'problem_solving', label: 'Problem Solving & Critical Thinking' },
          { value: 'project_management', label: 'Project Management' },
          { value: 'data_analysis', label: 'Data Analysis & Research' },
          { value: 'technical_programming', label: 'Programming & Technical Development' },
          { value: 'design_creative', label: 'Design & Creative Work' },
          { value: 'sales_marketing', label: 'Sales & Marketing' },
          { value: 'financial_analysis', label: 'Financial Analysis & Planning' },
          { value: 'writing_content', label: 'Writing & Content Creation' },
          { value: 'customer_service', label: 'Customer Service & Relations' },
          { value: 'teaching_training', label: 'Teaching & Training' },
          { value: 'strategic_planning', label: 'Strategic Planning & Vision' },
          { value: 'operations', label: 'Operations & Process Improvement' },
          { value: 'networking', label: 'Networking & Relationship Building' },
        ],
      },
      {
        key: 'skill_development_interest',
        text: 'Which new skills are you most interested in developing?',
        description: 'Select areas where you\'d like to grow or learn something completely new.',
        type: 'multipleChoice',
        allowMultiple: true,
        required: false,
        options: [
          { value: 'digital_marketing', label: 'Digital Marketing & Social Media' },
          { value: 'entrepreneurship', label: 'Entrepreneurship & Business Development' },
          { value: 'coding_tech', label: 'Coding & Technology' },
          { value: 'creative_arts', label: 'Creative Arts & Design' },
          { value: 'consulting', label: 'Consulting & Advisory Services' },
          { value: 'public_speaking', label: 'Public Speaking & Presentation' },
          { value: 'financial_planning', label: 'Financial Planning & Investment' },
          { value: 'coaching_mentoring', label: 'Coaching & Mentoring' },
          { value: 'content_creation', label: 'Content Creation & Media' },
          { value: 'e_commerce', label: 'E-commerce & Online Business' },
          { value: 'health_wellness', label: 'Health & Wellness Services' },
          { value: 'education_training', label: 'Education & Training' },
          { value: 'none', label: 'I prefer to focus on my existing skills' },
        ],
      },
      {
        key: 'learning_preference',
        text: 'How do you prefer to learn new skills?',
        description: 'Understanding your learning style helps us recommend appropriate resources.',
        type: 'multipleChoice',
        allowMultiple: true,
        required: true,
        options: [
          { value: 'online_courses', label: 'Online Courses & Tutorials' },
          { value: 'hands_on_practice', label: 'Hands-on Practice & Experimentation' },
          { value: 'mentorship', label: 'Mentorship & One-on-One Guidance' },
          { value: 'books_reading', label: 'Books & Written Materials' },
          { value: 'workshops_seminars', label: 'Workshops & Seminars' },
          { value: 'peer_learning', label: 'Peer Learning & Study Groups' },
          { value: 'formal_education', label: 'Formal Education & Certification Programs' },
          { value: 'youtube_videos', label: 'YouTube & Video Content' },
        ],
      },
    ],
  },
  {
    step: 4,
    title: 'Values and Work Preferences',
    questions: [
      {
        key: 'core_values',
        text: 'What values are most important to you in your work life?',
        description: 'Select up to 5 values that matter most to you in a career.',
        type: 'multipleChoice',
        allowMultiple: true,
        required: true,
        options: [
          { value: 'autonomy', label: 'Autonomy & Independence' },
          { value: 'creativity', label: 'Creativity & Innovation' },
          { value: 'helping_others', label: 'Helping Others & Making a Difference' },
          { value: 'financial_security', label: 'Financial Security & Stability' },
          { value: 'work_life_balance', label: 'Work-Life Balance' },
          { value: 'continuous_learning', label: 'Continuous Learning & Growth' },
          { value: 'recognition', label: 'Recognition & Achievement' },
          { value: 'collaboration', label: 'Collaboration & Teamwork' },
          { value: 'flexibility', label: 'Flexibility & Variety' },
          { value: 'leadership', label: 'Leadership & Influence' },
          { value: 'stability', label: 'Stability & Predictability' },
          { value: 'challenge', label: 'Challenge & Problem Solving' },
          { value: 'purpose', label: 'Purpose & Meaningful Work' },
          { value: 'location_freedom', label: 'Location Freedom & Remote Work' },
        ],
      },
      {
        key: 'work_environment_preference',
        text: 'What type of work environment do you thrive in?',
        description: 'Consider where and how you do your best work.',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: 'remote_home', label: 'Remote from Home' },
          { value: 'remote_flexible', label: 'Remote with Flexible Locations' },
          { value: 'hybrid', label: 'Hybrid (Mix of Remote and Office)' },
          { value: 'traditional_office', label: 'Traditional Office Environment' },
          { value: 'coworking_space', label: 'Co-working Spaces' },
          { value: 'client_sites', label: 'Client Sites or Field Work' },
          { value: 'no_preference', label: 'No Strong Preference' },
        ],
      },
      {
        key: 'team_size_preference',
        text: 'What team size do you prefer to work with?',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: 'solo', label: 'Solo Work (Independent)' },
          { value: 'small_team', label: 'Small Team (2-5 people)' },
          { value: 'medium_team', label: 'Medium Team (6-15 people)' },
          { value: 'large_team', label: 'Large Team (16+ people)' },
          { value: 'varies', label: 'Varies by Project' },
          { value: 'no_preference', label: 'No Preference' },
        ],
      },
      {
        key: 'risk_tolerance',
        text: 'How would you rate your tolerance for career-related risk?',
        description: 'Consider factors like income variability, job security, and uncertainty. 1 = Very risk-averse, 5 = Very comfortable with risk',
        type: 'scale',
        minLabel: 'Risk-Averse',
        maxLabel: 'Risk-Comfortable',
        numberOfSteps: 5,
        required: true,
      },
    ],
  },
  {
    step: 5,
    title: 'Career Transition Readiness',
    questions: [
      {
        key: 'transition_timeline',
        text: 'What is your ideal timeline for making a career change?',
        description: 'Be realistic about your current situation and constraints.',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: 'immediate', label: 'Immediately (Within 1 month)' },
          { value: 'short_term', label: 'Short-term (1-3 months)' },
          { value: 'medium_term', label: 'Medium-term (3-6 months)' },
          { value: 'long_term', label: 'Long-term (6-12 months)' },
          { value: 'extended', label: 'Extended planning (1+ years)' },
          { value: 'exploring', label: 'Just exploring options' },
        ],
      },
      {
        key: 'biggest_obstacles',
        text: 'What do you see as the biggest obstacles to your career change?',
        description: 'Select all that apply. Identifying barriers helps us provide targeted guidance.',
        type: 'multipleChoice',
        allowMultiple: true,
        required: true,
        options: [
          { value: 'financial_constraints', label: 'Financial Constraints' },
          { value: 'lack_of_skills', label: 'Lack of Required Skills' },
          { value: 'fear_of_failure', label: 'Fear of Failure' },
          { value: 'family_obligations', label: 'Family Obligations' },
          { value: 'lack_of_network', label: 'Lack of Professional Network' },
          { value: 'age_concerns', label: 'Age-related Concerns' },
          { value: 'location_constraints', label: 'Location Constraints' },
          { value: 'lack_of_experience', label: 'Lack of Experience in Target Field' },
          { value: 'imposter_syndrome', label: 'Imposter Syndrome' },
          { value: 'unclear_direction', label: 'Unclear About Direction' },
          { value: 'time_constraints', label: 'Time Constraints' },
          { value: 'other', label: 'Other' },
        ],
      },
      {
        key: 'support_system',
        text: 'How would you rate your support system for making a career change?',
        description: 'Consider family, friends, mentors, and professional network. 1 = Very little support, 5 = Strong support system',
        type: 'scale',
        minLabel: 'Little Support',
        maxLabel: 'Strong Support',
        numberOfSteps: 5,
        required: true,
      },
      {
        key: 'career_change_motivation',
        text: 'What is your primary motivation for considering a career change?',
        description: 'Choose the single most important factor driving your decision.',
        type: 'multipleChoice',
        required: true,
        options: [
          { value: 'unhappy_current', label: 'Unhappy with Current Situation' },
          { value: 'pursue_passion', label: 'Want to Pursue a Passion' },
          { value: 'better_compensation', label: 'Seeking Better Compensation' },
          { value: 'work_life_balance', label: 'Improve Work-Life Balance' },
          { value: 'growth_opportunities', label: 'Seeking Growth Opportunities' },
          { value: 'meaningful_work', label: 'Want More Meaningful Work' },
          { value: 'entrepreneurship', label: 'Entrepreneurial Aspirations' },
          { value: 'location_flexibility', label: 'Need Location Flexibility' },
          { value: 'industry_changes', label: 'Industry Changes/Disruption' },
          { value: 'personal_circumstances', label: 'Personal Life Changes' },
        ],
      },
      {
        key: 'confidence_level',
        text: 'How confident do you feel about successfully making a career change?',
        description: 'Be honest about your current confidence level. 1 = Very uncertain, 5 = Very confident',
        type: 'scale',
        minLabel: 'Very Uncertain',
        maxLabel: 'Very Confident',
        numberOfSteps: 5,
        required: true,
      },
    ],
  },
  {
    step: 6,
    title: 'Additional Insights',
    questions: [
      {
        key: 'ideal_day_description',
        text: 'Describe your ideal work day in a few sentences.',
        description: 'What would a perfect day at work look like for you? Consider tasks, environment, interactions, and schedule.',
        type: 'text',
        placeholder: 'Example: I would start my day at 9 AM, work on creative projects for 4 hours, have lunch with colleagues, then spend the afternoon in meetings and planning...',
        maxLength: 500,
        required: false,
      },
      {
        key: 'career_inspiration',
        text: 'Who or what inspires you in your career aspirations?',
        description: 'This could be a person, company, story, or experience that motivates your career goals.',
        type: 'text',
        placeholder: 'Share what or who inspires your career vision...',
        maxLength: 300,
        required: false,
      },
      {
        key: 'additional_thoughts',
        text: 'Is there anything else you\'d like us to know about your career situation or goals?',
        description: 'Share any additional context, concerns, or aspirations that weren\'t covered in the previous questions.',
        type: 'text',
        placeholder: 'Any additional thoughts, concerns, or goals you\'d like to share...',
        maxLength: 500,
        required: false,
      },
    ],
  },
];
// --- End Enhanced Assessment Definition ---

// Helper function to get all questions in a flat array with their required status
export const getAllQuestions = (): (Question & { step: number })[] => {
  return assessmentDefinition.flatMap(step => 
    step.questions.map(q => ({ ...q, step: step.step }))
  );
};

export const getQuestionByKey = (key: string): (Question & { step: number }) | undefined => {
  return getAllQuestions().find(q => q.key === key);
}; 