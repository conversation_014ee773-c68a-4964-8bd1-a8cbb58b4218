import * as Sentry from '@sentry/nextjs';
import React from 'react';

export interface ErrorContext {
  userId?: string;
  userEmail?: string;
  action?: string;
  component?: string;
  additionalData?: Record<string, any>;
}

export class ErrorReporter {
  /**
   * Report an error to <PERSON><PERSON> with additional context
   */
  static captureError(error: Error, context?: ErrorContext) {
    Sentry.withScope((scope) => {
      if (context?.userId) {
        scope.setUser({ id: context.userId, email: context.userEmail });
      }
      
      if (context?.action) {
        scope.setTag('action', context.action);
      }
      
      if (context?.component) {
        scope.setTag('component', context.component);
      }
      
      if (context?.additionalData) {
        scope.setContext('additionalData', context.additionalData);
      }
      
      Sentry.captureException(error);
    });
  }

  /**
   * Report a message to <PERSON><PERSON> (for non-error events)
   */
  static captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context?: ErrorContext) {
    Sentry.withScope((scope) => {
      if (context?.userId) {
        scope.setUser({ id: context.userId, email: context.userEmail });
      }
      
      if (context?.action) {
        scope.setTag('action', context.action);
      }
      
      if (context?.component) {
        scope.setTag('component', context.component);
      }
      
      if (context?.additionalData) {
        scope.setContext('additionalData', context.additionalData);
      }
      
      Sentry.captureMessage(message, level);
    });
  }

  /**
   * Add breadcrumb for debugging
   */
  static addBreadcrumb(message: string, category?: string, data?: Record<string, any>) {
    Sentry.addBreadcrumb({
      message,
      category: category || 'custom',
      data,
      level: 'info',
    });
  }

  /**
   * Set user context for all subsequent error reports
   */
  static setUser(userId: string, email?: string, additionalData?: Record<string, any>) {
    Sentry.setUser({
      id: userId,
      email,
      ...additionalData,
    });
  }

  /**
   * Clear user context
   */
  static clearUser() {
    Sentry.setUser(null);
  }

  /**
   * Performance monitoring - start transaction
   */
  static startTransaction(name: string, operation: string) {
    // Note: startTransaction is deprecated in newer Sentry versions
    // Using startSpan instead for performance monitoring
    return Sentry.startSpan({ name, op: operation }, () => {});
  }

  /**
   * Performance monitoring - measure function execution
   */
  static async measureAsync<T>(
    name: string,
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    return Sentry.startSpan({ name, op: operation }, async () => {
      try {
        const result = await fn();
        return result;
      } catch (error) {
        Sentry.captureException(error);
        throw error;
      }
    });
  }
}

/**
 * Higher-order component for automatic error boundary with Sentry reporting
 */
export function withErrorReporting<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
): React.ComponentType<P> {
  const WithErrorReportingComponent: React.ComponentType<P> = (props: P) => {
    React.useEffect(() => {
      ErrorReporter.addBreadcrumb(
        `Component ${componentName || WrappedComponent.name} mounted`,
        'component',
        { componentName: componentName || WrappedComponent.name }
      );
    }, []);

    return React.createElement(WrappedComponent, props);
  };

  WithErrorReportingComponent.displayName = `withErrorReporting(${componentName || WrappedComponent.name})`;

  return WithErrorReportingComponent;
}

/**
 * Hook for error reporting in functional components
 */
export function useErrorReporting() {
  const reportError = React.useCallback((error: Error, context?: ErrorContext) => {
    ErrorReporter.captureError(error, context);
  }, []);

  const reportMessage = React.useCallback((
    message: string, 
    level: 'info' | 'warning' | 'error' = 'info', 
    context?: ErrorContext
  ) => {
    ErrorReporter.captureMessage(message, level, context);
  }, []);

  const addBreadcrumb = React.useCallback((
    message: string, 
    category?: string, 
    data?: Record<string, any>
  ) => {
    ErrorReporter.addBreadcrumb(message, category, data);
  }, []);

  return {
    reportError,
    reportMessage,
    addBreadcrumb,
  };
}
