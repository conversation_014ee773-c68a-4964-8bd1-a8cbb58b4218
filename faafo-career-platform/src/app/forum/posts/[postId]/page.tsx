'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, User, Calendar, MessageSquare, Send, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import UserProfileCard from '@/components/forum/UserProfileCard';
import ReactionButton from '@/components/forum/ReactionButton';

interface ForumPost {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt?: string;
  category?: string;
  tags?: string[];
  viewCount?: number;
  author: {
    id: string;
    email: string;
    name?: string;
    profile?: {
      firstName?: string;
      lastName?: string;
      jobTitle?: string;
      company?: string;
      location?: string;
      profilePictureUrl?: string;
      experienceLevel?: string;
      bio?: string;
      profileCompletionScore?: number;
    };
    _count?: {
      forumPosts?: number;
      forumReplies?: number;
      achievements?: number;
    };
  };
  replies: Array<{
    id: string;
    content: string;
    createdAt: string;
    author: {
      id: string;
      email: string;
      name?: string;
      profile?: {
        firstName?: string;
        lastName?: string;
        jobTitle?: string;
        company?: string;
        profilePictureUrl?: string;
        experienceLevel?: string;
      };
    };
  }>;
}

interface ReplyForm {
  content: string;
}

export default function ForumPostPage({ params }: { params: Promise<{ postId: string }> }) {
  const { status } = useSession();
  const router = useRouter();
  const [post, setPost] = useState<ForumPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<ReplyForm>();

  const contentValue = watch('content', '');

  const fetchPost = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/forum/posts');

      if (!response.ok) {
        throw new Error('Failed to fetch posts');
      }

      const posts = await response.json();
      const resolvedParams = await params;
      const foundPost = posts.find((p: ForumPost) => p.id === resolvedParams.postId);

      if (!foundPost) {
        setError('Post not found');
        return;
      }

      setPost(foundPost);
    } catch (err) {
      console.error('Error fetching post:', err);
      setError('Failed to load post');
    } finally {
      setLoading(false);
    }
  }, [params]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchPost();
    }
  }, [status, fetchPost, router]);

  const onSubmitReply = async (data: ReplyForm) => {
    if (status !== 'authenticated') {
      setError('You must be logged in to reply');
      return;
    }

    setIsSubmittingReply(true);
    setError(null);

    try {
      const resolvedParams = await params;
      const response = await fetch(`/api/forum/posts/${resolvedParams.postId}/replies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: data.content.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create reply');
      }

      const newReply = await response.json();
      
      // Add the new reply to the post
      if (post) {
        setPost({
          ...post,
          replies: [...post.replies, newReply],
        });
      }
      
      reset(); // Clear the form
    } catch (err) {
      console.error('Error creating reply:', err);
      setError(err instanceof Error ? err.message : 'Failed to create reply');
    } finally {
      setIsSubmittingReply(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDisplayName = (author: { name?: string; email: string }) => {
    return author.name || author.email.split('@')[0];
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading post...</div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to view this post.</p>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">{error || 'Post not found'}</p>
          <Link href="/forum" className="text-blue-600 hover:underline mt-4 inline-block">
            Back to Forum
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-6">
        <Link
          href="/forum"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Forum
        </Link>
      </div>

      {/* Main Post */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <div className="space-y-6">
          {/* Post Header */}
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {post.title}
              </h1>
              {post.category && (
                <div className="mb-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {post.category}
                  </span>
                </div>
              )}
            </div>
            {post.viewCount !== undefined && (
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <Eye className="h-4 w-4 mr-1" />
                <span>{post.viewCount} views</span>
              </div>
            )}
          </div>

          {/* Post Content */}
          <div className="prose dark:prose-invert max-w-none">
            <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {post.content}
            </p>
          </div>

          {/* Author Profile */}
          <UserProfileCard
            user={{
              ...post.author,
              joinedAt: post.createdAt,
            }}
            variant="full"
            showStats={true}
          />

          {/* Post Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                <span>{formatDate(post.createdAt)}</span>
              </div>
              <div className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-1" />
                <span>{post.replies.length} {post.replies.length === 1 ? 'reply' : 'replies'}</span>
              </div>
            </div>

            {/* Reactions */}
            <ReactionButton postId={post.id} variant="full" />
          </div>
        </div>
      </div>

      {/* Replies Section */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Replies ({post.replies.length})
        </h2>

        {post.replies.map((reply) => (
          <div
            key={reply.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ml-8"
          >
            <div className="space-y-4">
              {/* Reply Content */}
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {reply.content}
              </p>

              {/* Reply Author */}
              <UserProfileCard
                user={{
                  ...reply.author,
                  joinedAt: reply.createdAt,
                }}
                variant="compact"
                showStats={false}
              />

              {/* Reply Footer */}
              <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>{formatDate(reply.createdAt)}</span>
                </div>

                {/* Reply Reactions */}
                <ReactionButton replyId={reply.id} variant="compact" />
              </div>
            </div>
          </div>
        ))}

        {/* Reply Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Add a Reply
          </h3>

          {error && (
            <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmitReply)} className="space-y-4">
            <div>
              <textarea
                id="content"
                rows={4}
                {...register('content', {
                  required: 'Reply content is required',
                  maxLength: {
                    value: 2000,
                    message: 'Reply must be 2000 characters or less',
                  },
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Write your reply..."
                disabled={isSubmittingReply}
              />
              <div className="flex justify-between mt-1">
                {errors.content && (
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.content.message}</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 ml-auto">
                  {contentValue.length}/2000 characters
                </p>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isSubmittingReply}
              className="flex items-center gap-2"
            >
              {isSubmittingReply ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Posting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  Post Reply
                </>
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
