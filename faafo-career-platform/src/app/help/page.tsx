import React from 'react';
import fs from 'fs';
import path from 'path';
import HelpSearch from '../../components/ui/HelpSearch';
import Link from 'next/link';

interface HelpDocument {
  title: string;
  content: string;
  url: string;
}

// Function to parse markdown content into HelpDocument format
const parseMarkdownToHelpDocuments = (filePath: string, baseUrl: string): HelpDocument[] => {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const lines = fileContent.split('\n');
  const documents: HelpDocument[] = [];
  let currentTitle = '';
  let currentContent: string[] = [];

  const addDocument = () => {
    if (currentTitle && currentContent.length > 0) {
      // Sanitize title for URL: replace spaces with hyphens, remove special chars
      const urlTitle = currentTitle.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-');
      documents.push({
        title: currentTitle,
        content: currentContent.join('\n'),
        url: `${baseUrl}#${urlTitle}`,
      });
    }
  };

  for (const line of lines) {
    if (line.startsWith('## ')) { // Top-level section
      addDocument(); // Add previous section as a document
      currentTitle = line.substring(3).trim();
      currentContent = [];
    } else if (line.startsWith('### ')) { // Sub-section
      addDocument(); // Add previous sub-section as a document
      currentTitle = line.substring(4).trim();
      currentContent = [];
    } else {
      currentContent.push(line);
    }
  }
  addDocument(); // Add the last document

  return documents;
};

export default function HelpPage() {
  const docsDirectory = path.join(process.cwd(), 'faafo-career-platform', 'docs');

  const userGuidePath = path.join(docsDirectory, 'user-guide.md');
  const faqPath = path.join(docsDirectory, 'faq-troubleshooting.md');

  let allHelpDocuments: HelpDocument[] = [];

  try {
    if (fs.existsSync(userGuidePath)) {
      allHelpDocuments = allHelpDocuments.concat(parseMarkdownToHelpDocuments(userGuidePath, '/help/user-guide'));
    }
    if (fs.existsSync(faqPath)) {
      allHelpDocuments = allHelpDocuments.concat(parseMarkdownToHelpDocuments(faqPath, '/help/faq'));
    }
  } catch (error) {
    console.error('Error reading help documents:', error);
    // In a real application, you might want to show an error message to the user
    // or provide a fallback. For now, we'll proceed with empty documents.
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8 text-center">Help Center</h1>
      <HelpSearch documents={allHelpDocuments} />

      {/* Optionally display all documents below the search bar */}
      <div className="mt-12 space-y-8">
        {allHelpDocuments.map((doc, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
            <h2 id={doc.url.split('#')[1]} className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              {doc.title}
            </h2>
            <div className="prose dark:prose-invert max-w-none">
              {doc.content.split('\n').map((paragraph, pIdx) => (
                <p key={pIdx}>{paragraph}</p>
              ))}
            </div>
            <Link href={doc.url} className="text-blue-600 dark:text-blue-400 hover:underline mt-4 inline-block">
              Link to section
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
} 