import { NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email'; // Adjust path if necessary
import React from 'react'; // Import React
import { ContactFormEmail } from '@/emails/ContactFormEmail'; // Import the new email template

export async function POST(request: Request) {
  try {
    const { name, email, subject, message } = await request.json();

    if (!name || !email || !subject || !message) {
      return NextResponse.json({ error: 'All fields are required.' }, { status: 400 });
    }

    // Basic email format validation (more robust validation can be added)
    if (!/^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
      return NextResponse.json({ error: 'Invalid email address.' }, { status: 400 });
    }

    await sendEmail({
      to: process.env.SUPPORT_EMAIL || '<EMAIL>', // Use an environment variable for support email
      subject: `Contact Form Submission: ${subject}`,
      template: React.createElement(ContactFormEmail, { name, email, subject, message }),
    });

    return NextResponse.json({ message: 'Your message has been sent successfully!' }, { status: 200 });

  } catch (error) {
    console.error('Error handling contact form submission:', error);
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON in request body.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'Failed to send message.' }, { status: 500 });
  }
} 