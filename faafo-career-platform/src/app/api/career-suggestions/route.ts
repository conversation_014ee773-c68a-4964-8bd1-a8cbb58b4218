import { NextRequest, NextResponse } from 'next/server';
import { getCareerPathSuggestions } from '@/lib/suggestionService';
import prisma from '@/lib/prisma'; // Corrected import for default export

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const assessmentId = searchParams.get('assessmentId');

  if (!assessmentId) {
    return NextResponse.json({ error: 'assessmentId is required' }, { status: 400 });
  }

  try {
    // Validate that the assessment exists
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
    });

    if (!assessment) {
      return NextResponse.json({ error: 'Assessment not found' }, { status: 404 });
    }

    const suggestions = await getCareerPathSuggestions(assessmentId);
    return NextResponse.json(suggestions);
  } catch (error) {
    console.error('Error fetching career suggestions:', error);
    // It's good practice to hide internal server errors from the client
    // and log them for debugging.
    if (error instanceof Error && error.message.includes("not found")) {
         return NextResponse.json({ error: 'Failed to fetch career suggestions due to missing related data.' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Failed to fetch career suggestions' }, { status: 500 });
  }
} 