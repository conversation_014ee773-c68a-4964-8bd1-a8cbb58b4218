import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

// GET handler to retrieve user goals
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const category = searchParams.get('category');

    const whereClause: any = {
      userId: session.user.id,
    };

    if (status) {
      whereClause.status = status;
    }

    if (type) {
      whereClause.type = type;
    }

    if (category) {
      whereClause.category = category;
    }

    const goals = await prisma.userGoal.findMany({
      where: whereClause,
      orderBy: [
        { status: 'asc' }, // Active goals first
        { createdAt: 'desc' },
      ],
    });

    // Calculate goal statistics
    const stats = {
      total: goals.length,
      active: goals.filter(g => g.status === 'ACTIVE').length,
      completed: goals.filter(g => g.status === 'COMPLETED').length,
      paused: goals.filter(g => g.status === 'PAUSED').length,
      cancelled: goals.filter(g => g.status === 'CANCELLED').length,
    };

    return NextResponse.json({
      goals,
      stats,
    });
  } catch (error) {
    console.error('Error fetching goals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch goals' },
      { status: 500 }
    );
  }
}

// POST handler to create a new goal
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      title,
      description,
      type,
      category,
      targetValue,
      targetDate,
      isPublic,
    } = body;

    // Validation
    if (!title || !type || !category || !targetValue) {
      return NextResponse.json(
        { error: 'Title, type, category, and target value are required' },
        { status: 400 }
      );
    }

    if (targetValue <= 0) {
      return NextResponse.json(
        { error: 'Target value must be greater than 0' },
        { status: 400 }
      );
    }

    // Validate enums
    const validTypes = ['DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY', 'CUSTOM'];
    const validCategories = [
      'LEARNING_RESOURCES',
      'SKILLS',
      'CERTIFICATIONS',
      'PROJECTS',
      'CAREER_MILESTONES',
      'NETWORKING',
    ];

    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Invalid goal type' },
        { status: 400 }
      );
    }

    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid goal category' },
        { status: 400 }
      );
    }

    const goal = await prisma.userGoal.create({
      data: {
        userId: session.user.id,
        title: title.trim(),
        description: description?.trim(),
        type,
        category,
        targetValue,
        targetDate: targetDate ? new Date(targetDate) : null,
        isPublic: isPublic || false,
      },
    });

    return NextResponse.json(goal, { status: 201 });
  } catch (error) {
    console.error('Error creating goal:', error);
    return NextResponse.json(
      { error: 'Failed to create goal' },
      { status: 500 }
    );
  }
}

// PUT handler to update a goal
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      id,
      title,
      description,
      targetValue,
      currentValue,
      targetDate,
      status,
      isPublic,
    } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Goal ID is required' },
        { status: 400 }
      );
    }

    // Check if goal exists and belongs to user
    const existingGoal = await prisma.userGoal.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!existingGoal) {
      return NextResponse.json(
        { error: 'Goal not found' },
        { status: 404 }
      );
    }

    const updateData: any = {};

    if (title !== undefined) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description?.trim();
    if (targetValue !== undefined) updateData.targetValue = targetValue;
    if (currentValue !== undefined) updateData.currentValue = currentValue;
    if (targetDate !== undefined) updateData.targetDate = targetDate ? new Date(targetDate) : null;
    if (status !== undefined) updateData.status = status;
    if (isPublic !== undefined) updateData.isPublic = isPublic;

    // If marking as completed, set completedAt
    if (status === 'COMPLETED' && existingGoal.status !== 'COMPLETED') {
      updateData.completedAt = new Date();
    }

    // If changing from completed to another status, clear completedAt
    if (status !== 'COMPLETED' && existingGoal.status === 'COMPLETED') {
      updateData.completedAt = null;
    }

    const updatedGoal = await prisma.userGoal.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json(updatedGoal);
  } catch (error) {
    console.error('Error updating goal:', error);
    return NextResponse.json(
      { error: 'Failed to update goal' },
      { status: 500 }
    );
  }
}

// DELETE handler to delete a goal
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Goal ID is required' },
        { status: 400 }
      );
    }

    // Check if goal exists and belongs to user
    const goal = await prisma.userGoal.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!goal) {
      return NextResponse.json(
        { error: 'Goal not found' },
        { status: 404 }
      );
    }

    await prisma.userGoal.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Goal deleted successfully' });
  } catch (error) {
    console.error('Error deleting goal:', error);
    return NextResponse.json(
      { error: 'Failed to delete goal' },
      { status: 500 }
    );
  }
}
