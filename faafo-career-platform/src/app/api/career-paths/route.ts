import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (id) {
      const careerPath = await prisma.careerPath.findUnique({
        where: { id },
        include: {
          learningResources: {
            where: { isActive: true },
            include: {
              ratings: {
                select: {
                  rating: true
                }
              }
            },
            orderBy: [
              { skillLevel: 'asc' },
              { cost: 'asc' }
            ]
          },
          relatedSkills: true,
          relatedIndustries: true
        }
      });

      if (careerPath) {
        // Parse JSON strings back to arrays and add rating info
        const formattedPath = {
          ...careerPath,
          pros: JSON.parse(careerPath.pros),
          cons: JSON.parse(careerPath.cons),
          actionableSteps: Array.isArray(careerPath.actionableSteps)
            ? careerPath.actionableSteps
            : JSON.parse(careerPath.actionableSteps as string),
          learningResources: careerPath.learningResources.map(resource => ({
            ...resource,
            averageRating: resource.ratings.length > 0
              ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
              : 0,
            totalRatings: resource.ratings.length
          }))
        };
        return NextResponse.json(formattedPath);
      }
      return new NextResponse('Career Path not found', { status: 404 });
    }

    const careerPaths = await prisma.careerPath.findMany({
      where: { isActive: true },
      include: {
        learningResources: {
          where: { isActive: true },
          select: {
            id: true,
            title: true,
            skillLevel: true,
            cost: true
          },
          take: 3 // Just show a few resources in the list view
        },
        relatedSkills: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: { name: 'asc' },
    });

    // Format the career paths for the frontend
    const formattedPaths = careerPaths.map(path => ({
      ...path,
      pros: JSON.parse(path.pros),
      cons: JSON.parse(path.cons),
      actionableSteps: Array.isArray(path.actionableSteps)
        ? path.actionableSteps
        : JSON.parse(path.actionableSteps as string),
    }));

    return NextResponse.json({ careerPaths: formattedPaths });
  } catch (error) {
    console.error('Error fetching career paths:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, pathId, itemId, completed, isBookmarked } = body;

    // Basic validation
    if (!action) {
      return new NextResponse('Action is required', { status: 400 });
    }

    switch (action) {
      case 'bookmark':
        if (!pathId || isBookmarked === undefined) {
          return new NextResponse('pathId and isBookmarked are required for bookmark action', { status: 400 });
        }
        console.log(`Bookmark action received for path: ${pathId}, status: ${isBookmarked}`);
        return NextResponse.json({ message: `Career path ${pathId} bookmarked: ${isBookmarked}.` });

      case 'update-checklist-item':
        if (!pathId || itemId === undefined || completed === undefined) {
          return new NextResponse('pathId, itemId, and completed are required for update-checklist-item action', { status: 400 });
        }
        // In a real application, you'd update the checklist item in a database
        console.log(`Update checklist item received for path: ${pathId}, item: ${itemId}, completed: ${completed}`);
        return NextResponse.json({ message: `Checklist item ${itemId} for path ${pathId} updated successfully.` });

      default:
        return new NextResponse('Invalid action', { status: 400 });
    }
  } catch (error) {
    console.error('Error processing career path POST request:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
} 