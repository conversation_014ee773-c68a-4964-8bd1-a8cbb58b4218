import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import prisma from '../../../lib/prisma';
import { signupSchema } from '../../../lib/validation';
import { withE<PERSON>r<PERSON><PERSON><PERSON>, createSuccessResponse, createError } from '../../../lib/errorHandler';
import { withRateLimit, rateLimitConfigs } from '../../../lib/rateLimit';

export const POST = withErrorHandler(async (request: NextRequest) => {
  return withRateLimit(request, rateLimitConfigs.signup, async () => {
    const body = await request.json();

    // Validate input
    const validatedData = signupSchema.parse(body);
    const { email, password } = validatedData;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (existingUser) {
      throw createError('ALREADY_EXISTS', 'User already exists', 409);
    }

    // Hash the password with stronger rounds
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create the new user
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        password: hashedPassword,
      },
      select: {
        id: true,
        email: true,
        createdAt: true
      }
    });

    return createSuccessResponse(
      { user },
      "User created successfully",
      201
    );
  });
});