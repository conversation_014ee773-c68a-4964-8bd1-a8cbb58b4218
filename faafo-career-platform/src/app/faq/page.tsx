'use client';

import React, { useState, useMemo } from 'react';
import { ChevronDown, ChevronUp, Search } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    category: "General",
    question: "What is FAAFO Career Platform?",
    answer: "FAAFO is a comprehensive career transition platform that helps individuals navigate significant career changes through self-assessment tools, financial planning resources, career path exploration, and community support."
  },
  {
    category: "General",
    question: "Is FAAFO free to use?",
    answer: "Yes, FAAFO offers core features for free including assessments, career path exploration, Freedom Fund calculator, and community forum access."
  },
  {
    category: "Account",
    question: "How do I create an account?",
    answer: "Click the 'Sign Up' button in the navigation bar, enter your email and password, then follow the onboarding process to complete your profile."
  },
  {
    category: "Account",
    question: "I forgot my password. How can I reset it?",
    answer: "On the login page, click 'Forgot Password?' and enter your email address. We'll send you a secure link to reset your password."
  },
  {
    category: "Assessment",
    question: "How long does the career assessment take?",
    answer: "The comprehensive assessment typically takes 15-20 minutes to complete. You can save your progress and return to it later if needed."
  },
  {
    category: "Assessment",
    question: "Can I retake the assessment?",
    answer: "Yes, you can retake the assessment at any time. Your most recent results will be used for career recommendations."
  },
  {
    category: "Freedom Fund",
    question: "What is a Freedom Fund?",
    answer: "A Freedom Fund is your emergency savings specifically designed to support you during career transitions. It typically covers 3-12 months of living expenses."
  },
  {
    category: "Freedom Fund",
    question: "How much should I save in my Freedom Fund?",
    answer: "This depends on your monthly expenses and risk tolerance. Our calculator helps you determine the right amount based on your situation, typically 6-12 months of expenses."
  },
  {
    category: "Community",
    question: "How do I participate in the forum?",
    answer: "After creating an account, navigate to the Forum section. You can browse existing discussions, create new posts, and reply to others' posts."
  },
  {
    category: "Community",
    question: "What are the community guidelines?",
    answer: "We encourage respectful, constructive discussions. No spam, harassment, or off-topic content. Be supportive and helpful to fellow community members."
  },
  {
    category: "Technical",
    question: "The page isn't loading correctly. What should I do?",
    answer: "Try refreshing the page first. If issues persist, clear your browser cache and cookies, or try a different browser. Ensure you have a stable internet connection."
  },
  {
    category: "Technical",
    question: "How do I report a bug or technical issue?",
    answer: "Use our Contact form <NAME_EMAIL> with details about the issue, including what you were doing when it occurred and any error messages."
  },
  {
    category: "Assessment",
    question: "What if I'm not sure about my answers?",
    answer: "It's okay to be uncertain! Answer based on your current feelings and situation. You can always retake the assessment as your thoughts and circumstances evolve."
  },
  {
    category: "Assessment",
    question: "How are career suggestions generated?",
    answer: "Our system analyzes your assessment responses including your dissatisfaction triggers, desired outcomes, skills, and financial situation to suggest relevant career paths."
  },
  {
    category: "Career Paths",
    question: "What if I don't like the suggested career paths?",
    answer: "Career suggestions are starting points for exploration. You can browse all available paths manually, retake the assessment with different responses, or use the resources to explore other options."
  },
  {
    category: "Career Paths",
    question: "Can I bookmark career paths for later?",
    answer: "Yes! You can bookmark interesting career paths and track your progress through their actionable steps. All bookmarked paths are saved to your profile."
  },
  {
    category: "Freedom Fund",
    question: "Can I update my Freedom Fund goal?",
    answer: "Absolutely! Your financial situation and goals may change. You can update your monthly expenses, target months, and current savings amount at any time."
  },
  {
    category: "Freedom Fund",
    question: "Should I include all expenses in my calculation?",
    answer: "Focus on essential expenses like housing, food, utilities, insurance, and minimum debt payments. You can reduce discretionary spending during your transition period."
  },
  {
    category: "General",
    question: "Is my personal information secure?",
    answer: "Yes, we take privacy seriously. All data is encrypted, we follow industry security standards, and we never share your personal information with third parties."
  },
  {
    category: "General",
    question: "Can I use FAAFO if I'm not ready to quit my job yet?",
    answer: "Absolutely! FAAFO is designed for all stages of career transition, from initial exploration to active job searching. Many users start planning months or years before making a change."
  },
  {
    category: "Community",
    question: "Can I post anonymously in the forum?",
    answer: "Currently, all posts are associated with your account name. However, you can use a pseudonym when creating your account if you prefer more privacy."
  },
  {
    category: "Technical",
    question: "Does FAAFO work on mobile devices?",
    answer: "Yes! FAAFO is fully responsive and works on smartphones, tablets, and desktop computers. You can access all features from any device with an internet connection."
  }
];

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = ['All', ...Array.from(new Set(faqData.map(item => item.category)))];

  const filteredFAQ = useMemo(() => {
    let filtered = faqData;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(item =>
        item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [selectedCategory, searchTerm]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-4xl font-bold mb-6 text-center">Frequently Asked Questions</h1>
      <p className="text-center text-gray-600 dark:text-gray-400 mb-8">
        Find answers to common questions about using FAAFO Career Platform.
      </p>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative max-w-md mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Search FAQs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>

      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 justify-center">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Results Count */}
        <div className="text-center mt-4 text-sm text-gray-600 dark:text-gray-400">
          Showing {filteredFAQ.length} of {faqData.length} questions
        </div>
      </div>

      {/* FAQ Items */}
      <div className="space-y-4">
        {filteredFAQ.map((item, index) => (
          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <button
              onClick={() => toggleItem(index)}
              className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div>
                <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  {item.category}
                </span>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {item.question}
                </h3>
              </div>
              {openItems.includes(index) ? (
                <ChevronUp className="h-5 w-5 text-gray-500" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-500" />
              )}
            </button>
            {openItems.includes(index) && (
              <div className="px-6 pb-4">
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {item.answer}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Contact Section */}
      <div className="mt-12 text-center bg-gray-50 dark:bg-gray-800 rounded-lg p-8">
        <h2 className="text-2xl font-bold mb-4">Still have questions?</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Can&apos;t find the answer you&apos;re looking for? We&apos;re here to help!
        </p>
        <a
          href="/contact"
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
        >
          Contact Support
        </a>
      </div>
    </div>
  );
}
