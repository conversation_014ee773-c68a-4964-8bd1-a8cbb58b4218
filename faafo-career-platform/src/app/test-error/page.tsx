'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useErrorReporting } from '@/lib/errorReporting';
import { Alert<PERSON>riangle, Bug, Zap } from 'lucide-react';

export default function TestErrorPage() {
  const [count, setCount] = useState(0);
  const { reportError, reportMessage, addBreadcrumb } = useErrorReporting();

  const throwError = () => {
    addBreadcrumb('User clicked throw error button', 'user_action');
    throw new Error('This is a test error for Sentry monitoring');
  };

  const reportCustomError = () => {
    addBreadcrumb('User clicked report custom error button', 'user_action');
    const error = new Error('Custom error reported manually');
    reportError(error, {
      action: 'test_custom_error',
      component: 'test_error_page',
      additionalData: { count, timestamp: new Date().toISOString() }
    });
  };

  const reportTestMessage = () => {
    addBreadcrumb('User clicked report message button', 'user_action');
    reportMessage('Test message from error monitoring demo', 'info', {
      action: 'test_message',
      component: 'test_error_page',
      additionalData: { count }
    });
  };

  const simulateAsyncError = async () => {
    addBreadcrumb('User clicked async error button', 'user_action');
    try {
      await new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Async operation failed')), 1000);
      });
    } catch (error) {
      reportError(error as Error, {
        action: 'test_async_error',
        component: 'test_error_page',
        additionalData: { count, operation: 'async_simulation' }
      });
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Error Monitoring Test Page</h1>
        <p className="text-gray-600 dark:text-gray-400">
          This page demonstrates the Sentry error monitoring integration for Phase 2 development.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Error Boundary Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Error Boundary Test
            </CardTitle>
            <CardDescription>
              Test the React Error Boundary with automatic Sentry reporting
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Click count: {count}
            </p>
            <div className="space-y-2">
              <Button 
                onClick={() => setCount(c => c + 1)}
                variant="outline"
                className="w-full"
              >
                Increment Counter
              </Button>
              <Button 
                onClick={throwError}
                variant="destructive"
                className="w-full"
              >
                <Bug className="h-4 w-4 mr-2" />
                Throw Error (Error Boundary)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Manual Error Reporting */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              Manual Error Reporting
            </CardTitle>
            <CardDescription>
              Test manual error reporting without breaking the UI
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              onClick={reportCustomError}
              variant="outline"
              className="w-full"
            >
              Report Custom Error
            </Button>
            <Button
              onClick={reportTestMessage}
              variant="outline"
              className="w-full"
            >
              Report Info Message
            </Button>
            <Button 
              onClick={simulateAsyncError}
              variant="outline"
              className="w-full"
            >
              Simulate Async Error
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Information Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Error Monitoring Features</CardTitle>
          <CardDescription>
            Phase 2 error monitoring capabilities implemented
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">✅ Implemented Features:</h4>
              <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-400">
                <li>• React Error Boundary with Sentry integration</li>
                <li>• Manual error reporting utilities</li>
                <li>• User context tracking</li>
                <li>• Breadcrumb logging</li>
                <li>• Performance monitoring setup</li>
                <li>• Custom error context</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">🔧 Configuration:</h4>
              <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-400">
                <li>• Sentry SDK integrated</li>
                <li>• Environment variables configured</li>
                <li>• Error boundary in root layout</li>
                <li>• API error reporting added</li>
                <li>• Development/production modes</li>
                <li>• Source map upload ready</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
        <p className="text-sm text-yellow-800 dark:text-yellow-200">
          <strong>Note:</strong> To fully activate Sentry monitoring, add your Sentry DSN to the environment variables. 
          For development, errors will be logged to the console even without Sentry configuration.
        </p>
      </div>
    </div>
  );
}
