import { Html, Head, Body, Container, Text, Button, Tailwind } from '@react-email/components';
import * as React from 'react';

interface VerificationEmailProps {
  username: string;
  verificationLink: string;
}

export const VerificationEmail = ({ username, verificationLink }: VerificationEmailProps) => (
  <Html>
    <Head />
    <Tailwind>
      <Body className="bg-white my-auto mx-auto font-sans px-2">
        <Container className="border border-solid border-[#eaeaea] rounded my-8 mx-auto p-4 md:p-8">
          <Text className="text-black text-xl font-bold">FAAFO Career Platform</Text>
          <Text className="text-black text-base leading-6">Hi {username},</Text>
          <Text className="text-black text-base leading-6">
            Welcome to FAAFO Career Platform! To get started, please verify your email address by clicking the button below:
          </Text>
          <Button
            className="bg-blue-500 rounded text-white text-base font-semibold no-underline text-center px-5 py-3"
            href={verificationLink}
          >
            Verify Email
          </Button>
          <Text className="text-black text-base leading-6">
            If you did not sign up for an account, please ignore this email.
          </Text>
          <Text className="text-black text-base leading-6">
            Best regards,
            <br />
            The FAAFO Career Platform Team
          </Text>
        </Container>
      </Body>
    </Tailwind>
  </Html>
);

export default VerificationEmail; 