import { Html, Head, Body, Container, Text, Link, Img } from '@react-email/components';
import React from 'react';

interface PasswordResetEmailProps {
  resetLink: string;
}

export const PasswordResetEmail = ({ resetLink }: PasswordResetEmailProps) => (
  <Html lang="en">
    <Head />
    <Body style={main}>
      <Container style={container}>
        <Img
          src="https://res.cloudinary.com/example/image/upload/v1678901234/logo.png"
          width="170"
          height="50"
          alt="FAAFO Logo"
          style={logo}
        />
        <Text style={paragraph}>Hi there,</Text>
        <Text style={paragraph}>
          You recently requested to reset your password for your FAAFO account.
          Click the button below to reset it.
        </Text>
        <Link href={resetLink} style={button}>
          Reset your password
        </Link>
        <Text style={paragraph}>
          If you did not request a password reset, please ignore this email or
          contact support if you have any questions.
        </Text>
        <Text style={paragraph}>Thanks,</Text>
        <Text style={paragraph}>The FAAFO Team</Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const logo = {
  margin: '0 auto',
};

const paragraph = {
  color: '#444',
  fontSize: '15px',
  lineHeight: '24px',
  textAlign: 'left' as const,
};

const button = {
  backgroundColor: '#2e70c2',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '15px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '210px',
  padding: '10px 0',
  margin: '20px auto',
}; 