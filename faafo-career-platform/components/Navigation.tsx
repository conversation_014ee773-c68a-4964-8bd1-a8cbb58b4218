"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { Home, User, LogIn, UserPlus, LogOut, Sun, Moon, Menu as MenuIcon, X as XIcon } from 'lucide-react';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const [theme, setTheme] = useState('light');

  const toggleMenu = () => {
    const currentlyOpen = isOpen;
    setIsOpen(!currentlyOpen);
    if (currentlyOpen && menuButtonRef.current) {
      menuButtonRef.current.focus();
    }
  };

  // Escape key to close mobile menu
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
        if (menuButtonRef.current) menuButtonRef.current.focus();
      }
    };
    if (isOpen) document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen]);

  // Focus trapping for mobile menu
  useEffect(() => {
    if (!isOpen || !mobileMenuRef.current) return;
    const focusableElements = mobileMenuRef.current.querySelectorAll(
      'a[href]:not([disabled]), button:not([disabled]), textarea:not([disabled]), input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    if (focusableElements.length === 0) return;
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    firstElement.focus();
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;
      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  // Theme management
  useEffect(() => {
    if (theme === 'dark') document.documentElement.classList.add('dark');
    else document.documentElement.classList.remove('dark');
    localStorage.setItem('theme', theme);
  }, [theme]);

  useEffect(() => {
    const storedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null;
    const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    setTheme(storedTheme || (prefersDark ? 'dark' : 'light'));
  }, []);

  const toggleTheme = () => setTheme(prev => prev === 'light' ? 'dark' : 'light');

  return (
    <nav className="relative flex items-center justify-between w-full py-3">
      {/* Logo or Site Title */}
      <Link href="/" className="text-lg font-bold dark:text-white">
        My App
      </Link>

      {/* Desktop Navigation & Theme Toggle */}
      <div className="hidden md:flex items-center gap-x-3">
        <ul className="flex items-center gap-x-1">
          <li><NavLink href="/"><Home size={18} /> Home</NavLink></li>
          <li><NavLink href="#features">Features</NavLink></li>
          <AuthLinksDesktop />
        </ul>
        <button 
          onClick={toggleTheme} 
          aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300"
        >
          {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
        </button>
      </div>

      {/* Mobile Menu Toggle Button (only for mobile) */}
      <div className="md:hidden flex items-center">
         <button 
          onClick={toggleTheme} 
          aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 mr-2"
        >
          {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
        </button>
        <button
          ref={menuButtonRef}
          aria-label="Toggle navigation menu"
          aria-expanded={isOpen}
          aria-controls="mobile-menu"
          onClick={toggleMenu}
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300"
        >
          {isOpen ? <XIcon size={24} /> : <MenuIcon size={24} />}
        </button>
      </div>
      
      {/* Mobile Menu (Dropdown) */}
      {isOpen && (
        <div
          id="mobile-menu"
          ref={mobileMenuRef}
          className="md:hidden absolute top-full left-0 mt-2 w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 z-50 transition-all duration-300 ease-out transform opacity-100 translate-y-0"
        >
          <ul className="flex flex-col gap-3">
            <li><NavLink href="/" onClick={toggleMenu} isMobile><Home size={18} /> Home</NavLink></li>
            <li><NavLink href="#features" onClick={toggleMenu} isMobile>Features</NavLink></li>
            <AuthLinksMobile toggleMenu={toggleMenu} />
          </ul>
        </div>
      )}
    </nav>
  );
};

// Reusable NavLink component for consistent styling
const NavLink = ({ href, children, onClick, isMobile }: { href: string; children: React.ReactNode; onClick?: () => void; isMobile?: boolean}) => (
  <Link 
    href={href} 
    onClick={onClick}
    className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700 ${
      isMobile ? 'text-gray-700 dark:text-gray-200 w-full' : 'text-gray-600 dark:text-gray-300'
    }`}
  >
    {children}
  </Link>
);

// AuthLinks for Desktop
const AuthLinksDesktop = () => {
  const { data: session, status } = useSession();
  if (status === 'loading') return null;
  return session ? (
    <>
      <li><NavLink href="/profile"><User size={18} /> Profile</NavLink></li>
      <li><button onClick={() => signOut()} className="flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 w-full text-left"><LogOut size={18} /> Sign Out</button></li>
    </>
  ) : (
    <>
      <li><NavLink href="/login"><LogIn size={18} /> Sign In</NavLink></li>
      <li><NavLink href="/signup"><UserPlus size={18} /> Sign Up</NavLink></li>
    </>
  );
};

// AuthLinks for Mobile (passes toggleMenu to close on click)
const AuthLinksMobile = ({ toggleMenu }: { toggleMenu: () => void }) => {
  const { data: session, status } = useSession();
  if (status === 'loading') return null;
  return session ? (
    <>
      <li><NavLink href="/profile" onClick={toggleMenu} isMobile><User size={18} /> Profile</NavLink></li>
      <li><button onClick={() => { signOut(); toggleMenu(); }} className="flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 w-full text-left"><LogOut size={18} /> Sign Out</button></li>
    </>
  ) : (
    <>
      <li><NavLink href="/login" onClick={toggleMenu} isMobile><LogIn size={18} /> Sign In</NavLink></li>
      <li><NavLink href="/signup" onClick={toggleMenu} isMobile><UserPlus size={18} /> Sign Up</NavLink></li>
    </>
  );
};

export default Navigation; 