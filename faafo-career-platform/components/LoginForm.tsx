"use client";

import React, { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);

    const result = await signIn('credentials', {
      redirect: false, // Prevent automatic redirect
      email,
      password,
    });

    if (result?.error) {
      setError(result.error);
    } else {
      // Successful login, explicitly redirect to the home page
      router.push('/');
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="mt-8 w-full max-w-sm"
      aria-describedby={error ? "login-form-error" : undefined}
    >
      <div className="mb-4">
        <label htmlFor="email" className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">Email Address</label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="appearance-none border border-gray-200 dark:border-gray-700 rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight"
          placeholder="Enter your email"
          required
        />
      </div>
      <div className="mb-6">
        <label htmlFor="password" className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">Password</label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="appearance-none border border-gray-200 dark:border-gray-700 rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight"
          placeholder="Enter your password"
          required
        />
      </div>
      <div className="flex items-center justify-between">
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Sign In
        </button>
        <Link href="/auth/forgot-password" className="inline-block align-baseline font-bold text-sm text-blue-600 hover:text-blue-800">
          Forgot Password?
        </Link>
      </div>
      {error && (
        <p 
          id="login-form-error"
          className="mt-4 text-center text-sm text-red-600"
          role="alert"
        >
          {error}
        </p>
      )}
    </form>
  );
};

export default LoginForm; 