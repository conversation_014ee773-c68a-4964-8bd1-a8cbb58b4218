'use client';

import React from 'react';
import { use<PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';

interface FreedomFundFormInput {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavings?: number;
}

interface FreedomFundCalculatorFormProps {
  onSubmit: (data: FreedomFundFormInput) => void;
  // Potentially pass initial data if loading existing record
  initialData?: Partial<FreedomFundFormInput>; 
}

const coverageOptions = [3, 6, 9, 12];

export default function FreedomFundCalculatorForm({
  onSubmit,
  initialData = {},
}: FreedomFundCalculatorFormProps) {
  const { control, handleSubmit, formState: { errors } } = useForm<FreedomFundFormInput>({
    defaultValues: {
      monthlyExpenses: initialData.monthlyExpenses,
      coverageMonths: initialData.coverageMonths ?? 6,
      currentSavings: initialData.currentSavings,
    },
    mode: "onBlur",
  });

  const handleFormSubmit: SubmitHandler<FreedomFundFormInput> = (data) => {
    // Convert numeric string inputs to numbers before submitting
    const numericData = {
      ...data,
      monthlyExpenses: parseFloat(String(data.monthlyExpenses)),
      currentSavings: data.currentSavings ? parseFloat(String(data.currentSavings)) : undefined,
      coverageMonths: parseInt(String(data.coverageMonths), 10),
    };
    onSubmit(numericData);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6 bg-white dark:bg-gray-800 p-8 rounded-lg shadow-xl">
      <div>
        <label htmlFor="monthlyExpenses" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Monthly Essential Expenses ($)
        </label>
        <Controller
          name="monthlyExpenses"
          control={control}
          rules={{
            required: 'Monthly expenses are required.',
            min: { value: 0.01, message: 'Must be greater than 0.' },
          }}
          render={({ field }) => (
            <input
              {...field}
              type="number"
              id="monthlyExpenses"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              step="0.01"
            />
          )}
        />
        {errors.monthlyExpenses && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400" role="alert">
            {errors.monthlyExpenses.message}
          </p>
        )}
      </div>

      <div>
        <label htmlFor="coverageMonths" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Desired Coverage (Months)
        </label>
        <Controller
          name="coverageMonths"
          control={control}
          defaultValue={6} // Default to 6 months
          rules={{ required: 'Please select coverage months.' }}
          render={({ field }) => (
            <select
              {...field}
              id="coverageMonths"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              {coverageOptions.map(months => (
                <option key={months} value={months}>
                  {months} months
                </option>
              ))}
            </select>
          )}
        />
        {errors.coverageMonths && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400" role="alert">
            {errors.coverageMonths.message}
          </p>
        )}
      </div>

      <div>
        <label htmlFor="currentSavings" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Current Savings Amount ($) (Optional)
        </label>
        <Controller
          name="currentSavings"
          control={control}
          rules={{
            min: { value: 0, message: 'Cannot be negative.' },
          }}
          render={({ field }) => (
            <input
              {...field}
              value={field.value === null || field.value === undefined ? '' : field.value}
              type="number"
              id="currentSavings"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              step="0.01"
              placeholder="0.00"
            />
          )}
        />
        {errors.currentSavings && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400" role="alert">
            {errors.currentSavings.message}
          </p>
        )}
      </div>

      <div>
        <button
          type="submit"
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
        >
          Calculate & Save Target
        </button>
      </div>
    </form>
  );
} 