'use client';

import React from 'react';

interface FreedomFundResultsProps {
  targetAmount: number | null;
  currentSavings: number | null;
}

export default function FreedomFundResults({
  targetAmount,
  currentSavings,
}: FreedomFundResultsProps) {
  if (targetAmount === null) {
    return null; // Don't render if no target is calculated yet
  }

  const progressPercentage = currentSavings !== null && targetAmount > 0 
    ? Math.min((currentSavings / targetAmount) * 100, 100) 
    : 0;
  
  const amountRemaining = currentSavings !== null 
    ? Math.max(targetAmount - currentSavings, 0)
    : targetAmount;

  const ariaValueNow = Number(progressPercentage.toFixed(0));
  const ariaValueMin = 0;
  const ariaValueMax = 100;

  return (
    <div className="mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl space-y-4">
      <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 text-center">Your Freedom Fund Target</h2>
      
      <div className="text-center">
        <p className="text-4xl font-bold text-blue-600 dark:text-blue-400">
          ${targetAmount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
        </p>
      </div>

      {currentSavings !== null && (
        <div className="mt-6">
          <h3 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">Progress</h3>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-6">
            <div 
              className="bg-green-500 h-6 rounded-full text-xs font-medium text-white text-center p-1 leading-none"
              style={{ width: `${progressPercentage}%` }}
              role="progressbar"
              aria-valuenow={ariaValueNow}
              aria-valuemin={ariaValueMin}
              aria-valuemax={ariaValueMax}
            >
              {progressPercentage.toFixed(0)}%
            </div>
          </div>
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-2">
            <span>
              Saved: ${currentSavings.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
            <span>
              Remaining: ${amountRemaining.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </div>
        </div>
      )}
    </div>
  );
} 