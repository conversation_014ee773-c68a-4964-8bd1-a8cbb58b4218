"use client";

import React, { useState } from 'react';

const SignupForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formMessage, setFormMessage] = useState<{ type: 'info' | 'success' | 'error'; content: string } | null>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setFormMessage({ type: 'info', content: 'Signing up...' });

    try {
      const response = await fetch('/api/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      // Check if the response is JSON before parsing
      const contentType = response.headers.get('content-type');
      let data = { message: 'An unexpected error occurred' }; // Default error message

      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json();
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError);
          // If JSON parsing fails, data remains the default error message
        }
      } else {
        // If response is not JSON, try to read as text
        const textResponse = await response.text();
        console.error('Non-JSON response received:', textResponse);
        data.message = 'Received non-JSON response from server.';
      }

      if (response.ok) {
        setFormMessage({ type: 'success', content: 'Success: ' + data.message });
        // Optionally redirect or clear form
      } else {
        setFormMessage({ type: 'error', content: 'Error: ' + (data.message || 'An unexpected error occurred') });
      }
    } catch (error) {
      console.error('Signup error:', error);
      setFormMessage({ type: 'error', content: 'Error: An unexpected error occurred.' });
    }
  };

  return (
    <form 
      onSubmit={handleSubmit} 
      className="mt-8 w-full max-w-sm"
      aria-describedby={formMessage ? "signup-form-message" : undefined}
    >
      <div className="mb-4">
        <label htmlFor="email" className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">Email Address</label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="appearance-none border border-gray-200 dark:border-gray-700 rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight"
          placeholder="Enter your email"
          required
        />
      </div>
      <div className="mb-6">
        <label htmlFor="password" className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">Password</label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="appearance-none border border-gray-200 dark:border-gray-700 rounded w-full py-2 px-3 text-gray-700 dark:text-gray-100 dark:placeholder-gray-500 leading-tight"
          placeholder="Create a password"
          required
        />
      </div>
      <div className="flex items-center justify-between">
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Sign Up
        </button>
      </div>
      <div className="mt-4 text-center text-xs text-gray-500 dark:text-gray-400">
        By signing up, you agree to our <a href="/terms-of-service" className="underline hover:text-blue-600">Terms of Service</a> and <a href="/privacy-policy" className="underline hover:text-blue-600">Privacy Policy</a>.
      </div>
      {formMessage && formMessage.type === 'error' && (
        <p
          id="signup-form-message"
          className="mt-4 text-center text-sm text-red-600"
          role="alert"
        >
          {formMessage.content}
        </p>
      )}
      {formMessage && (formMessage.type === 'success' || formMessage.type === 'info') && (
        <p
          id="signup-form-message"
          className={`mt-4 text-center text-sm ${ 
            formMessage.type === 'success' ? 'text-green-600' : 'text-blue-600'
          }`}
          role="status"
        >
          {formMessage.content}
        </p>
      )}
    </form>
  );
};

export default SignupForm; 