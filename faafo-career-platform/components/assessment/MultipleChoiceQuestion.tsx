import React from 'react';

interface Option {
  value: string;
  label: string;
}

interface MultipleChoiceQuestionProps {
  questionKey: string;
  options: Option[];
  currentSelection: string | string[]; // string for single select, string[] for multi-select
  onChange: (questionKey: string, selection: string | string[]) => void;
  allowMultiple?: boolean;
}

const MultipleChoiceQuestion: React.FC<MultipleChoiceQuestionProps> = ({
  questionKey,
  options,
  currentSelection,
  onChange,
  allowMultiple = false,
}) => {

  const handleSelectionChange = (optionValue: string) => {
    if (allowMultiple) {
      const currentArray = Array.isArray(currentSelection) ? currentSelection : (currentSelection ? [currentSelection] : []);
      const newSelection = currentArray.includes(optionValue)
        ? currentArray.filter(val => val !== optionValue)
        : [...currentArray, optionValue];
      onChange(questionKey, newSelection);
    } else {
      onChange(questionKey, optionValue);
    }
  };

  return (
    <fieldset className="space-y-3">
      <legend className="sr-only">{questionKey} options</legend>
      {options.map((option) => {
        const isSelected = allowMultiple
          ? Array.isArray(currentSelection) && currentSelection.includes(option.value)
          : currentSelection === option.value;
        
        return (
          <div key={option.value} className="flex items-center">
            <input
              id={`${questionKey}-${option.value}`}
              name={questionKey}
              type={allowMultiple ? 'checkbox' : 'radio'}
              value={option.value}
              checked={isSelected}
              onChange={() => handleSelectionChange(option.value)}
              className={`${allowMultiple ? 'h-4 w-4 rounded' : 'h-4 w-4'} border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:focus:ring-blue-600 dark:ring-offset-gray-800`}
            />
            <label htmlFor={`${questionKey}-${option.value}`} className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {option.label}
            </label>
          </div>
        );
      })}
    </fieldset>
  );
};

export default MultipleChoiceQuestion; 