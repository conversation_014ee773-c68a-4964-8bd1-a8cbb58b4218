import React from 'react';

interface QuestionWrapperProps {
  questionKey: string;
  questionText: string;
  questionDescription?: string;
  children: React.ReactNode;
  errorMessage?: string;
}

const QuestionWrapper: React.FC<QuestionWrapperProps> = ({
  questionKey,
  questionText,
  questionDescription,
  children,
  errorMessage,
}) => {
  return (
    <div className="mb-8 p-6 bg-white dark:bg-gray-800 shadow-md rounded-lg" id={`question-${questionKey}`}>
      <div className="flex items-center mb-2">
        <label htmlFor={questionKey} className="block text-xl font-semibold text-gray-800 dark:text-gray-100 mr-2">
          {questionText}
        </label>
        {questionDescription && (
          <div className="relative group">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5 text-gray-500 dark:text-gray-400 cursor-pointer"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712L12 16.5l-2.121-2.121m4.242 0A2.25 2.25 0 0 0 13.5 12h-1.5m0 0a2.25 2.25 0 0 1-2.25-2.25V9m3.75-9a1.875 1.875 0 0 1 1.875 1.875v.75M6 18H4.5M7.5 3h.375m0 0H7.5m4.5 0h.375m0 0H12m7.5 0h.375m0 0H19.5M4.5 18A2.25 2.25 0 0 0 2.25 20.25V21a.75.75 0 0 0 .75.75H5.25A2.25 2.25 0 0 0 7.5 21v-.75m0 0a2.25 2.25 0 0 1 2.25-2.25H12M12 21.75V21m0 0v-.75M18 18H19.5a2.25 2.25 0 0 0 2.25-2.25V15m0 0a2.25 2.25 0 0 0-2.25-2.25H18m0 0v.75m-4.5 0h.75m-7.5 0H9M4.5 18a2.25 2.25 0 0 1-2.25 2.25V21a.75.75 0 0 0 .75.75H5.25A2.25 2.25 0 0 0 7.5 21v-.75m0 0a2.25 2.25 0 0 1 2.25-2.25H12m-4.5 0h.75m-7.5 0H9m4.5 0h.75m-7.5 0H9"
              />
            </svg>
            <div className="absolute left-1/2 -translate-x-1/2 mt-2 w-64 p-3 bg-gray-700 text-white text-sm rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10">
              {questionDescription}
            </div>
          </div>
        )}
      </div>
      <div className="mt-4">
        {children}
      </div>
      {errorMessage && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400" role="alert">
          {errorMessage}
        </p>
      )}
    </div>
  );
};

export default QuestionWrapper; 