import React from 'react';

interface ScaleQuestionProps {
  questionKey: string;
  // questionText is handled by QuestionWrapper
  minLabel: string;
  maxLabel: string;
  numberOfSteps: number; // e.g., 5 for a 1-5 scale
  currentValue: number | null; // null if not answered
  onChange: (questionKey: string, value: number) => void;
}

const ScaleQuestion: React.FC<ScaleQuestionProps> = ({
  questionKey,
  minLabel,
  maxLabel,
  numberOfSteps,
  currentValue,
  onChange,
}) => {
  const steps = Array.from({ length: numberOfSteps }, (_, i) => i + 1);

  return (
    <div className="space-y-3">
      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 px-1 mb-2">
        <span>{minLabel}</span>
        <span>{maxLabel}</span>
      </div>
      <fieldset className="flex justify-around items-center border border-gray-300 dark:border-gray-600 rounded-md p-3">
        <legend className="sr-only">{questionKey} scale</legend>
        {steps.map((stepValue) => (
          <div key={stepValue} className="flex flex-col items-center mx-1">
            <input
              id={`${questionKey}-step-${stepValue}`}
              name={questionKey}
              type="radio"
              value={stepValue}
              checked={currentValue === stepValue}
              onChange={() => onChange(questionKey, stepValue)}
              className="h-5 w-5 border-gray-300 dark:border-gray-500 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
            />
            <label htmlFor={`${questionKey}-step-${stepValue}`} className="mt-1.5 text-xs text-gray-500 dark:text-gray-400">
              {stepValue}
            </label>
          </div>
        ))}
      </fieldset>
    </div>
  );
};

export default ScaleQuestion; 