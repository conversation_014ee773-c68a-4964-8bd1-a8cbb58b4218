import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/assessment',
  '/forum',
  '/freedom-fund',
  '/progress',
  '/recommendations',
];

// Define API routes that require authentication
const protectedApiRoutes = [
  '/api/assessment',
  '/api/profile',
  '/api/freedom-fund',
  '/api/learning-progress',
  '/api/personalized-resources',
  '/api/progress-tracker',
  '/api/recommendations',
  '/api/resource-ratings',
];

// Define public API routes that don't require authentication
const publicApiRoutes = [
  '/api/auth',
  '/api/signup',
  '/api/career-paths',
  '/api/learning-resources',
  '/api/contact',
  '/api/csrf-token',
];

// Rate limiting store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  return (request as any).ip || 'unknown';
}

function isRateLimited(request: NextRequest, windowMs: number = 15 * 60 * 1000, maxRequests: number = 100): boolean {
  const clientIP = getClientIP(request);
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // Clean up old entries
  Array.from(rateLimitStore.entries()).forEach(([key, value]) => {
    if (value.resetTime < windowStart) {
      rateLimitStore.delete(key);
    }
  });
  
  // Get or create entry for this IP
  const entry = rateLimitStore.get(clientIP) || { count: 0, resetTime: now + windowMs };
  
  // Reset if window has expired
  if (entry.resetTime < now) {
    entry.count = 0;
    entry.resetTime = now + windowMs;
  }
  
  // Increment count
  entry.count++;
  rateLimitStore.set(clientIP, entry);
  
  return entry.count > maxRequests;
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Add HSTS header for HTTPS
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  
  return response;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api/_next') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }
  
  // Apply rate limiting to API routes
  if (pathname.startsWith('/api/')) {
    // Different rate limits for different types of endpoints
    let windowMs = 15 * 60 * 1000; // 15 minutes
    let maxRequests = 100;
    
    if (pathname.startsWith('/api/auth/')) {
      windowMs = 15 * 60 * 1000; // 15 minutes
      maxRequests = 5; // Stricter for auth endpoints
    } else if (pathname === '/api/signup') {
      windowMs = 60 * 60 * 1000; // 1 hour
      maxRequests = 3; // Very strict for signup
    } else if (pathname === '/api/contact') {
      windowMs = 60 * 60 * 1000; // 1 hour
      maxRequests = 3; // Strict for contact form
    }
    
    if (isRateLimited(request, windowMs, maxRequests)) {
      return new NextResponse(
        JSON.stringify({ error: 'Too many requests' }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': '900', // 15 minutes
          },
        }
      );
    }
  }
  
  // Check if the route requires authentication
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isProtectedApiRoute = protectedApiRoutes.some(route => pathname.startsWith(route));
  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route));
  
  // Handle API routes
  if (pathname.startsWith('/api/')) {
    // Skip auth check for public API routes
    if (isPublicApiRoute) {
      const response = NextResponse.next();
      return addSecurityHeaders(response);
    }
    
    // Check authentication for protected API routes
    if (isProtectedApiRoute) {
      const token = await getToken({ 
        req: request, 
        secret: process.env.NEXTAUTH_SECRET 
      });
      
      if (!token) {
        return new NextResponse(
          JSON.stringify({ error: 'Unauthorized' }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }
    }
    
    const response = NextResponse.next();
    return addSecurityHeaders(response);
  }
  
  // Handle page routes
  if (isProtectedRoute) {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });
    
    if (!token) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', request.url);
      return NextResponse.redirect(loginUrl);
    }
  }
  
  // Handle auth pages (redirect if already logged in)
  if (pathname === '/login' || pathname === '/signup') {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });
    
    if (token) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }
  
  const response = NextResponse.next();
  return addSecurityHeaders(response);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
