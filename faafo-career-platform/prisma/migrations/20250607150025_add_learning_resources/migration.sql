-- AlterTable
ALTER TABLE "Skill" ADD COLUMN "category" TEXT;
ALTER TABLE "Skill" ADD COLUMN "description" TEXT;

-- CreateTable
CREATE TABLE "LearningResource" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "skillLevel" TEXT NOT NULL,
    "author" TEXT,
    "duration" TEXT,
    "cost" TEXT NOT NULL DEFAULT 'FREE',
    "format" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "UserLearningProgress" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'NOT_STARTED',
    "completedAt" DATETIME,
    "notes" TEXT,
    "rating" INTEGER,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "UserLearningProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "UserLearningProgress_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "LearningResource" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "_CareerPathToLearningResource" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_CareerPathToLearningResource_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_CareerPathToLearningResource_B_fkey" FOREIGN KEY ("B") REFERENCES "LearningResource" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "_SkillToLearningResource" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_SkillToLearningResource_A_fkey" FOREIGN KEY ("A") REFERENCES "LearningResource" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_SkillToLearningResource_B_fkey" FOREIGN KEY ("B") REFERENCES "Skill" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "LearningResource_url_key" ON "LearningResource"("url");

-- CreateIndex
CREATE UNIQUE INDEX "UserLearningProgress_userId_resourceId_key" ON "UserLearningProgress"("userId", "resourceId");

-- CreateIndex
CREATE UNIQUE INDEX "_CareerPathToLearningResource_AB_unique" ON "_CareerPathToLearningResource"("A", "B");

-- CreateIndex
CREATE INDEX "_CareerPathToLearningResource_B_index" ON "_CareerPathToLearningResource"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_SkillToLearningResource_AB_unique" ON "_SkillToLearningResource"("A", "B");

-- CreateIndex
CREATE INDEX "_SkillToLearningResource_B_index" ON "_SkillToLearningResource"("B");
