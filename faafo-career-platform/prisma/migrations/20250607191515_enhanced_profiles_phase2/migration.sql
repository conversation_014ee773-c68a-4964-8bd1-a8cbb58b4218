/*
  Warnings:

  - Added the required column `updatedAt` to the `Profile` table without a default value. This is not possible if the table is not empty.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Profile" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "bio" TEXT,
    "profilePictureUrl" TEXT,
    "socialMediaLinks" JSONB,
    "firstName" TEXT,
    "lastName" TEXT,
    "jobTitle" TEXT,
    "company" TEXT,
    "location" TEXT,
    "phoneNumber" TEXT,
    "website" TEXT,
    "careerInterests" JSONB,
    "skillsToLearn" JSONB,
    "experienceLevel" TEXT DEFAULT 'BEGINNER',
    "currentIndustry" TEXT,
    "targetIndustry" TEXT,
    "profileCompletionScore" INTEGER NOT NULL DEFAULT 0,
    "lastProfileUpdate" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "resumeUrl" TEXT,
    "resumeFileName" TEXT,
    "resumeUploadedAt" DATETIME,
    "weeklyLearningGoal" INTEGER NOT NULL DEFAULT 3,
    "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "profileVisibility" TEXT NOT NULL DEFAULT 'PRIVATE',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Profile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Profile" ("bio", "id", "profilePictureUrl", "socialMediaLinks", "userId", "updatedAt") SELECT "bio", "id", "profilePictureUrl", "socialMediaLinks", "userId", CURRENT_TIMESTAMP FROM "Profile";
DROP TABLE "Profile";
ALTER TABLE "new_Profile" RENAME TO "Profile";
CREATE UNIQUE INDEX "Profile_userId_key" ON "Profile"("userId");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
