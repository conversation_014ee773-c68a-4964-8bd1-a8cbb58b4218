import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import pluginSecurity from 'eslint-plugin-security';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    ignores: ["src/generated/prisma/**", "prisma/seed.ts"]
  },
  pluginSecurity.configs.recommended,
  {
    files: ["jest.setup.ts"],
    rules: {
      "no-var": "off"
    }
  }
];

export default eslintConfig;
