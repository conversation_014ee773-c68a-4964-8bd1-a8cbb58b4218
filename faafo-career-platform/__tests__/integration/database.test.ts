import { TestDatabase } from '../utils/testHelpers';
import { testUsers, testLearningResources, testProgressData, testRatings } from '../fixtures/testData';

describe('Database Integration Tests', () => {
  let testDb: TestDatabase;

  beforeAll(async () => {
    testDb = new TestDatabase();
  });

  beforeEach(async () => {
    await testDb.cleanup();
  });

  afterAll(async () => {
    await testDb.cleanup();
    await testDb.disconnect();
  });

  describe('User-Assessment Relationships', () => {
    it('should maintain referential integrity between users and assessments', async () => {
      const user = await testDb.createTestUser(testUsers.validUser);
      const assessment = await testDb.createTestAssessment(user.id);

      expect(assessment.userId).toBe(user.id);

      // Verify cascade behavior would be tested here
      // (depends on actual Prisma schema configuration)
    });

    it('should handle multiple assessments per user correctly', async () => {
      const user = await testDb.createTestUser(testUsers.validUser);
      
      // In a real scenario, business logic might prevent multiple active assessments
      // This tests the database's ability to handle the relationship
      const assessment1 = await testDb.createTestAssessment(user.id, { status: 'COMPLETED' });
      const assessment2 = await testDb.createTestAssessment(user.id, { status: 'IN_PROGRESS' });

      expect(assessment1.userId).toBe(user.id);
      expect(assessment2.userId).toBe(user.id);
      expect(assessment1.id).not.toBe(assessment2.id);
    });
  });

  describe('User-Resource Progress Relationships', () => {
    let testUser: any;
    let testResource: any;

    beforeEach(async () => {
      testUser = await testDb.createTestUser(testUsers.validUser);
      testResource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);
    });

    it('should track user progress across multiple resources', async () => {
      const resource2 = await testDb.createTestLearningResource({
        ...testLearningResources.dataScienceArticle,
        url: 'https://example.com/unique-url-2'
      });

      const progress1 = await testDb.createTestProgress(testUser.id, testResource.id, {
        status: 'COMPLETED',
        rating: 5
      });

      const progress2 = await testDb.createTestProgress(testUser.id, resource2.id, {
        status: 'IN_PROGRESS'
      });

      expect(progress1.userId).toBe(testUser.id);
      expect(progress2.userId).toBe(testUser.id);
      expect(progress1.resourceId).toBe(testResource.id);
      expect(progress2.resourceId).toBe(resource2.id);
      expect(progress1.status).toBe('COMPLETED');
      expect(progress2.status).toBe('IN_PROGRESS');
    });

    it('should enforce unique user-resource progress combination', async () => {
      await testDb.createTestProgress(testUser.id, testResource.id);

      await expect(
        testDb.createTestProgress(testUser.id, testResource.id)
      ).rejects.toThrow();
    });

    it('should allow multiple users to track progress on same resource', async () => {
      const user2 = await testDb.createTestUser({
        ...testUsers.validUser,
        email: '<EMAIL>'
      });

      const progress1 = await testDb.createTestProgress(testUser.id, testResource.id);
      const progress2 = await testDb.createTestProgress(user2.id, testResource.id);

      expect(progress1.resourceId).toBe(testResource.id);
      expect(progress2.resourceId).toBe(testResource.id);
      expect(progress1.userId).not.toBe(progress2.userId);
    });
  });

  describe('Rating System Integration', () => {
    let testUser: any;
    let testResource: any;

    beforeEach(async () => {
      testUser = await testDb.createTestUser(testUsers.validUser);
      testResource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);
    });

    it('should maintain separate rating and progress records', async () => {
      const progress = await testDb.createTestProgress(testUser.id, testResource.id, {
        status: 'COMPLETED',
        rating: 4,
        review: 'Good course'
      });

      const rating = await testDb.createTestRating(testUser.id, testResource.id, {
        rating: 5,
        review: 'Excellent course!',
        isHelpful: true
      });

      expect(progress.rating).toBe(4);
      expect(rating.rating).toBe(5);
      expect(progress.review).toBe('Good course');
      expect(rating.review).toBe('Excellent course!');
    });

    it('should calculate average ratings correctly', async () => {
      // Create multiple users and ratings
      const users = await Promise.all([
        testDb.createTestUser({ ...testUsers.validUser, email: '<EMAIL>' }),
        testDb.createTestUser({ ...testUsers.validUser, email: '<EMAIL>' }),
        testDb.createTestUser({ ...testUsers.validUser, email: '<EMAIL>' })
      ]);

      const ratings = [5, 4, 3];
      
      for (let i = 0; i < users.length; i++) {
        await testDb.createTestRating(users[i].id, testResource.id, {
          rating: ratings[i],
          review: `Review ${i + 1}`,
          isHelpful: true
        });
      }

      // In a real application, you'd query for average rating
      const expectedAverage = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
      expect(expectedAverage).toBe(4); // (5 + 4 + 3) / 3 = 4
    });

    it('should enforce unique user-resource rating combination', async () => {
      await testDb.createTestRating(testUser.id, testResource.id, testRatings.excellentRating);

      await expect(
        testDb.createTestRating(testUser.id, testResource.id, testRatings.goodRating)
      ).rejects.toThrow();
    });
  });

  describe('Data Consistency and Transactions', () => {
    it('should maintain data consistency during concurrent operations', async () => {
      const user = await testDb.createTestUser(testUsers.validUser);
      const resource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);

      // Simulate concurrent progress updates
      const concurrentOperations = Array.from({ length: 5 }, (_, i) => 
        testDb.createTestProgress(user.id, resource.id, {
          status: 'IN_PROGRESS',
          notes: `Concurrent update ${i}`
        }).catch(() => null) // Expect some to fail due to unique constraint
      );

      const results = await Promise.all(concurrentOperations);
      const successfulOperations = results.filter(result => result !== null);

      // Only one should succeed due to unique constraint
      expect(successfulOperations).toHaveLength(1);
    });

    it('should handle cascading deletes properly', async () => {
      const user = await testDb.createTestUser(testUsers.validUser);
      const assessment = await testDb.createTestAssessment(user.id);

      // Create assessment responses
      // Note: This would require implementing assessment response creation in TestDatabase
      // For now, we'll just verify the assessment exists
      expect(assessment.userId).toBe(user.id);

      // In a real test, you'd verify that deleting the assessment
      // also deletes related responses (if configured with CASCADE)
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      const startTime = performance.now();

      // Create multiple users and resources
      const users = await Promise.all(
        Array.from({ length: 50 }, (_, i) => 
          testDb.createTestUser({
            ...testUsers.validUser,
            email: `user${i}@example.com`
          })
        )
      );

      const resources = await Promise.all(
        Array.from({ length: 20 }, (_, i) => 
          testDb.createTestLearningResource({
            ...testLearningResources.cybersecurityCourse,
            title: `Resource ${i}`,
            url: `https://example.com/resource-${i}`
          })
        )
      );

      const endTime = performance.now();
      const executionTime = endTime - startTime;

      expect(users).toHaveLength(50);
      expect(resources).toHaveLength(20);
      expect(executionTime).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should handle complex queries efficiently', async () => {
      // Create test data
      const user = await testDb.createTestUser(testUsers.validUser);
      const resources = await Promise.all(
        Array.from({ length: 10 }, (_, i) => 
          testDb.createTestLearningResource({
            ...testLearningResources.cybersecurityCourse,
            title: `Resource ${i}`,
            url: `https://example.com/resource-${i}`,
            category: i % 2 === 0 ? 'CYBERSECURITY' : 'DATA_SCIENCE'
          })
        )
      );

      // Create progress records
      const progressRecords = await Promise.all(
        resources.map(resource => 
          testDb.createTestProgress(user.id, resource.id, {
            status: 'COMPLETED',
            rating: Math.floor(Math.random() * 5) + 1
          })
        )
      );

      expect(progressRecords).toHaveLength(10);

      // In a real application, you'd test complex queries here
      // For example: finding all completed cybersecurity courses with rating > 4
      const cybersecurityResources = resources.filter(r => r.category === 'CYBERSECURITY');
      expect(cybersecurityResources.length).toBeGreaterThan(0);
    });
  });

  describe('Data Validation and Constraints', () => {
    it('should enforce email uniqueness across users', async () => {
      await testDb.createTestUser(testUsers.validUser);

      await expect(
        testDb.createTestUser(testUsers.validUser)
      ).rejects.toThrow();
    });

    it('should enforce URL uniqueness across learning resources', async () => {
      await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);

      await expect(
        testDb.createTestLearningResource(testLearningResources.cybersecurityCourse)
      ).rejects.toThrow();
    });

    it('should validate enum values', async () => {
      // Test invalid assessment status
      await expect(
        testDb.createTestAssessment('user-id', { status: 'INVALID_STATUS' as any })
      ).rejects.toThrow();

      // Test invalid resource type
      await expect(
        testDb.createTestLearningResource({
          ...testLearningResources.cybersecurityCourse,
          url: 'https://example.com/invalid-type',
          type: 'INVALID_TYPE' as any
        })
      ).rejects.toThrow();
    });

    it('should validate rating ranges', async () => {
      const user = await testDb.createTestUser(testUsers.validUser);
      const resource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);

      // Test invalid rating values
      const invalidRatings = [0, 6, -1, 10];

      for (const invalidRating of invalidRatings) {
        await expect(
          testDb.createTestRating(user.id, resource.id, {
            rating: invalidRating,
            review: 'Test review'
          })
        ).rejects.toThrow();
      }
    });
  });

  describe('Backup and Recovery', () => {
    it('should maintain data integrity after simulated recovery', async () => {
      // Create initial data
      const user = await testDb.createTestUser(testUsers.validUser);
      const resource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);
      const progress = await testDb.createTestProgress(user.id, resource.id);

      // Verify data exists
      expect(user.id).toBeDefined();
      expect(resource.id).toBeDefined();
      expect(progress.id).toBeDefined();

      // Simulate recovery by reconnecting to database
      await testDb.disconnect();
      testDb = new TestDatabase();

      // Data should still be accessible (in a real scenario)
      // For this test, we'll just verify the new connection works
      const newUser = await testDb.createTestUser({
        ...testUsers.validUser,
        email: '<EMAIL>'
      });

      expect(newUser.id).toBeDefined();
    });
  });
});
