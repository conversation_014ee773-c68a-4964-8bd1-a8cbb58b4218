import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import PersonalizedResources from '../../src/components/dashboard/PersonalizedResources';
import { createMockFetch, mockNextRouter } from '../utils/testHelpers';
import { testLearningResources } from '../fixtures/testData';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => mockNextRouter
}));

// Mock NextAuth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn()
}));

// Mock UI components
jest.mock('../../src/components/ui/card', () => ({
  Card: ({ children, className, ...props }: any) => (
    <div data-testid="card" className={className} {...props}>{children}</div>
  ),
  CardContent: ({ children, ...props }: any) => (
    <div data-testid="card-content" {...props}>{children}</div>
  ),
  CardDescription: ({ children, ...props }: any) => (
    <div data-testid="card-description" {...props}>{children}</div>
  ),
  CardHeader: ({ children, ...props }: any) => (
    <div data-testid="card-header" {...props}>{children}</div>
  ),
  CardTitle: ({ children, ...props }: any) => (
    <div data-testid="card-title" {...props}>{children}</div>
  ),
}));

jest.mock('../../src/components/ui/button', () => ({
  Button: ({ children, onClick, className, ...props }: any) => (
    <button 
      data-testid="button" 
      onClick={onClick} 
      className={className} 
      {...props}
    >
      {children}
    </button>
  ),
}));

jest.mock('../../src/components/ui/badge', () => ({
  Badge: ({ children, variant, ...props }: any) => (
    <span data-testid="badge" data-variant={variant} {...props}>{children}</span>
  ),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  BookOpen: () => <div data-testid="book-open-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  Star: () => <div data-testid="star-icon" />,
  ExternalLink: () => <div data-testid="external-link-icon" />,
  Bookmark: () => <div data-testid="bookmark-icon" />,
  BookmarkCheck: () => <div data-testid="bookmark-check-icon" />,
  Loader2: () => <div data-testid="loader-icon" />,
}));

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

describe('PersonalizedResources Component', () => {
  const mockResources = [
    {
      id: '1',
      title: 'Ethical Hacking Fundamentals',
      description: 'Learn the basics of ethical hacking',
      url: 'https://example.com/ethical-hacking',
      type: 'COURSE',
      category: 'CYBERSECURITY',
      skillLevel: 'BEGINNER',
      author: 'Security Expert',
      duration: '40 hours',
      cost: 'FREE',
      averageRating: 4.5,
      totalRatings: 120,
      isBookmarked: false
    },
    {
      id: '2',
      title: 'Machine Learning Basics',
      description: 'Introduction to machine learning concepts',
      url: 'https://example.com/ml-basics',
      type: 'ARTICLE',
      category: 'DATA_SCIENCE',
      skillLevel: 'INTERMEDIATE',
      author: 'Data Scientist',
      duration: '2 hours',
      cost: 'FREE',
      averageRating: 4.2,
      totalRatings: 85,
      isBookmarked: true
    }
  ];

  beforeEach(() => {
    // Mock successful session
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'test-user-id', email: '<EMAIL>' },
        expires: '2024-12-31'
      },
      status: 'authenticated'
    });

    // Mock fetch for API calls
    global.fetch = createMockFetch([
      {
        url: '/api/personalized-resources',
        response: { success: true, data: mockResources }
      },
      {
        url: '/api/learning-progress',
        response: { success: true, message: 'Progress updated' }
      }
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render component with loading state', () => {
      render(<PersonalizedResources />);
      
      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('card-title')).toBeInTheDocument();
      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
    });

    it('should render resources after loading', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Machine Learning Basics')).toBeInTheDocument();
      });
    });

    it('should display resource details correctly', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        // Check first resource
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Learn the basics of ethical hacking')).toBeInTheDocument();
        expect(screen.getByText('Security Expert')).toBeInTheDocument();
        expect(screen.getByText('40 hours')).toBeInTheDocument();
        expect(screen.getByText('4.5')).toBeInTheDocument();
        expect(screen.getByText('(120 ratings)')).toBeInTheDocument();
      });
    });

    it('should show appropriate badges for resource types', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        const badges = screen.getAllByTestId('badge');
        expect(badges.length).toBeGreaterThan(0);
        
        // Check for category and skill level badges
        expect(screen.getByText('CYBERSECURITY')).toBeInTheDocument();
        expect(screen.getByText('BEGINNER')).toBeInTheDocument();
        expect(screen.getByText('DATA_SCIENCE')).toBeInTheDocument();
        expect(screen.getByText('INTERMEDIATE')).toBeInTheDocument();
      });
    });
  });

  describe('User Interactions', () => {
    it('should handle bookmark toggle', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const bookmarkButtons = screen.getAllByTestId('button');
      const bookmarkButton = bookmarkButtons.find(button => 
        button.textContent?.includes('Bookmark') || 
        button.querySelector('[data-testid="bookmark-icon"]')
      );

      if (bookmarkButton) {
        fireEvent.click(bookmarkButton);
        
        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            expect.stringContaining('/api/learning-progress'),
            expect.objectContaining({
              method: 'POST',
              headers: expect.objectContaining({
                'Content-Type': 'application/json'
              }),
              body: expect.stringContaining('BOOKMARKED')
            })
          );
        });
      }
    });

    it('should handle external link clicks', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const viewButtons = screen.getAllByTestId('button');
      const viewButton = viewButtons.find(button => 
        button.textContent?.includes('View Resource')
      );

      if (viewButton) {
        fireEvent.click(viewButton);
        
        // Should open external link (mocked behavior)
        expect(viewButton).toHaveBeenCalled;
      }
    });

    it('should handle progress tracking', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const startButtons = screen.getAllByTestId('button');
      const startButton = startButtons.find(button => 
        button.textContent?.includes('Start Learning')
      );

      if (startButton) {
        fireEvent.click(startButton);
        
        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            expect.stringContaining('/api/learning-progress'),
            expect.objectContaining({
              method: 'POST',
              body: expect.stringContaining('IN_PROGRESS')
            })
          );
        });
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      global.fetch = createMockFetch([
        {
          url: '/api/personalized-resources',
          response: { success: false, error: 'Failed to fetch resources' },
          status: 500
        }
      ]);

      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });

    it('should handle network errors', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });

    it('should handle empty resource list', async () => {
      global.fetch = createMockFetch([
        {
          url: '/api/personalized-resources',
          response: { success: true, data: [] }
        }
      ]);

      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText(/no resources/i)).toBeInTheDocument();
      });
    });
  });

  describe('Authentication States', () => {
    it('should handle unauthenticated user', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated'
      });

      render(<PersonalizedResources />);
      
      expect(screen.getByText(/sign in/i)).toBeInTheDocument();
    });

    it('should handle loading authentication state', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading'
      });

      render(<PersonalizedResources />);
      
      expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should render correctly on mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<PersonalizedResources />);
      
      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      
      // Check for responsive classes (this would depend on actual implementation)
      expect(card.className).toContain('w-full');
    });

    it('should render correctly on desktop', () => {
      // Mock desktop viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      });

      render(<PersonalizedResources />);
      
      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const buttons = screen.getAllByTestId('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('type');
      });
    });

    it('should support keyboard navigation', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const buttons = screen.getAllByTestId('button');
      buttons.forEach(button => {
        expect(button).not.toHaveAttribute('tabIndex', '-1');
      });
    });

    it('should have semantic HTML structure', () => {
      render(<PersonalizedResources />);
      
      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('card-header')).toBeInTheDocument();
      expect(screen.getByTestId('card-content')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('should not cause memory leaks', async () => {
      const { unmount } = render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
      });

      unmount();
      
      // Component should unmount cleanly
      expect(screen.queryByText('Personalized Learning Resources')).not.toBeInTheDocument();
    });

    it('should handle rapid state changes', async () => {
      const { rerender } = render(<PersonalizedResources />);
      
      // Rapidly change session state
      for (let i = 0; i < 10; i++) {
        mockUseSession.mockReturnValue({
          data: { user: { id: `user-${i}` }, expires: '2024-12-31' },
          status: 'authenticated'
        });
        
        rerender(<PersonalizedResources />);
      }
      
      // Should handle rapid changes without errors
      expect(screen.getByTestId('card')).toBeInTheDocument();
    });
  });
});
