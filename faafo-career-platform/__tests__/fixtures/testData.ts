// Test data fixtures for consistent testing

export const testUsers = {
  validUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test User'
  },
  adminUser: {
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    name: 'Admin User'
  },
  incompleteUser: {
    email: '<EMAIL>',
    // Missing password
    name: 'Incomplete User'
  },
  invalidEmailUser: {
    email: 'invalid-email',
    password: 'TestPassword123!',
    name: 'Invalid Email User'
  }
};

export const testAssessments = {
  completeAssessment: {
    dissatisfaction_triggers: ['lack_of_growth', 'poor_work_life_balance', 'limited_remote_options'],
    desired_outcomes_skill_a: 'high',
    desired_outcomes_skill_b: 'medium',
    desired_outcomes_skill_c: 'low',
    desired_outcomes_skill_d: 'high',
    desired_outcomes_skill_e: 'medium',
    work_environment_preference: 'remote',
    risk_tolerance: 'medium',
    learning_style: 'hands_on',
    time_commitment: '10_15_hours',
    career_change_timeline: '6_12_months',
    financial_situation: 'stable_income',
    support_system: 'family_friends',
    biggest_fear: 'financial_instability',
    motivation_source: 'personal_fulfillment'
  },
  partialAssessment: {
    dissatisfaction_triggers: ['lack_of_growth'],
    desired_outcomes_skill_a: 'high',
    work_environment_preference: 'hybrid'
  },
  invalidAssessment: {
    dissatisfaction_triggers: 'invalid_string_instead_of_array',
    desired_outcomes_skill_a: 'invalid_value',
    work_environment_preference: null
  }
};

export const testLearningResources = {
  cybersecurityCourse: {
    title: 'Ethical Hacking Fundamentals',
    description: 'Learn the basics of ethical hacking and penetration testing',
    url: 'https://example.com/ethical-hacking',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'Security Expert',
    duration: '40 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  dataScienceArticle: {
    title: 'Introduction to Machine Learning',
    description: 'A comprehensive guide to machine learning concepts',
    url: 'https://example.com/ml-intro',
    type: 'ARTICLE',
    category: 'DATA_SCIENCE',
    skillLevel: 'INTERMEDIATE',
    author: 'Data Scientist',
    duration: '2 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  webDevVideo: {
    title: 'React Hooks Deep Dive',
    description: 'Advanced React hooks patterns and best practices',
    url: 'https://example.com/react-hooks',
    type: 'VIDEO',
    category: 'WEB_DEVELOPMENT',
    skillLevel: 'ADVANCED',
    author: 'React Expert',
    duration: '3 hours',
    cost: 'PAID',
    format: 'INSTRUCTOR_LED'
  }
};

export const testCareerPaths = {
  cybersecurityAnalyst: {
    name: 'Cybersecurity Analyst',
    slug: 'cybersecurity-analyst',
    overview: 'Protect organizations from cyber threats and analyze security incidents',
    pros: ['High demand', 'Good salary', 'Remote work opportunities'],
    cons: ['High stress', 'Continuous learning required', 'On-call responsibilities'],
    actionableSteps: [
      { title: 'Get Security+ Certification', description: 'Obtain CompTIA Security+ certification' },
      { title: 'Learn Network Security', description: 'Study network security fundamentals' },
      { title: 'Practice with Labs', description: 'Use virtual labs for hands-on experience' }
    ]
  },
  dataScientist: {
    name: 'Data Scientist',
    slug: 'data-scientist',
    overview: 'Extract insights from data to drive business decisions',
    pros: ['High salary', 'Intellectual challenges', 'Growing field'],
    cons: ['Requires strong math skills', 'Data quality issues', 'Long analysis cycles'],
    actionableSteps: [
      { title: 'Learn Python/R', description: 'Master programming languages for data analysis' },
      { title: 'Study Statistics', description: 'Build strong statistical foundation' },
      { title: 'Work on Projects', description: 'Build portfolio with real-world projects' }
    ]
  }
};

export const testProgressData = {
  beginnerProgress: {
    status: 'IN_PROGRESS',
    notes: 'Making good progress on the fundamentals',
    rating: null,
    review: null
  },
  completedProgress: {
    status: 'COMPLETED',
    notes: 'Excellent course, learned a lot',
    rating: 5,
    review: 'Highly recommend this course for beginners',
    completedAt: new Date()
  },
  bookmarkedProgress: {
    status: 'BOOKMARKED',
    notes: 'Want to come back to this later',
    rating: null,
    review: null
  }
};

export const testRatings = {
  excellentRating: {
    rating: 5,
    review: 'Outstanding resource! Clear explanations and practical examples.',
    isHelpful: true
  },
  goodRating: {
    rating: 4,
    review: 'Good content but could use more examples.',
    isHelpful: true
  },
  poorRating: {
    rating: 2,
    review: 'Content was outdated and hard to follow.',
    isHelpful: false
  },
  ratingWithoutReview: {
    rating: 3,
    review: null,
    isHelpful: null
  }
};

export const testAPIResponses = {
  successResponse: {
    success: true,
    data: { id: '123', message: 'Operation successful' }
  },
  errorResponse: {
    success: false,
    error: 'Something went wrong'
  },
  unauthorizedResponse: {
    error: 'Unauthorized'
  },
  validationErrorResponse: {
    error: 'Validation failed',
    details: ['Email is required', 'Password must be at least 8 characters']
  }
};

export const testFormData = {
  validLoginForm: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  invalidLoginForm: {
    email: 'invalid-email',
    password: '123' // Too short
  },
  validSignupForm: {
    email: '<EMAIL>',
    password: 'NewPassword123!',
    confirmPassword: 'NewPassword123!'
  },
  invalidSignupForm: {
    email: '<EMAIL>',
    password: 'NewPassword123!',
    confirmPassword: 'DifferentPassword123!' // Passwords don't match
  }
};

export const testSecurityInputs = {
  sqlInjectionAttempts: [
    "'; DROP TABLE users; --",
    "1' OR '1'='1",
    "admin'--",
    "' UNION SELECT * FROM users --"
  ],
  xssAttempts: [
    "<script>alert('xss')</script>",
    "javascript:alert('xss')",
    "<img src=x onerror=alert('xss')>",
    "<svg onload=alert('xss')>"
  ],
  pathTraversalAttempts: [
    "../../../etc/passwd",
    "..\\..\\..\\windows\\system32\\config\\sam",
    "....//....//....//etc/passwd",
    "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
  ],
  oversizedInputs: {
    longString: "A".repeat(10000),
    longEmail: "a".repeat(1000) + "@example.com",
    longPassword: "P".repeat(1000) + "123!"
  }
};

export const testPerformanceData = {
  largeDataset: Array.from({ length: 1000 }, (_, i) => ({
    id: `resource-${i}`,
    title: `Resource ${i}`,
    description: `Description for resource ${i}`,
    category: ['CYBERSECURITY', 'DATA_SCIENCE', 'WEB_DEVELOPMENT'][i % 3]
  })),
  concurrentUsers: Array.from({ length: 50 }, (_, i) => ({
    email: `user${i}@example.com`,
    password: 'TestPassword123!'
  }))
};

export const testErrorScenarios = {
  networkError: new Error('Network request failed'),
  timeoutError: new Error('Request timeout'),
  serverError: new Error('Internal server error'),
  validationError: new Error('Validation failed'),
  authenticationError: new Error('Authentication failed'),
  authorizationError: new Error('Access denied')
};

export const testEnvironmentConfig = {
  testDatabaseUrl: 'file:./test.db',
  testApiBaseUrl: 'http://localhost:3000',
  testTimeout: 30000,
  maxRetries: 3
};
