// Basic test to verify Jest setup is working correctly

describe('Basic Test Suite', () => {
  it('should run basic arithmetic tests', () => {
    expect(2 + 2).toBe(4);
    expect(10 - 5).toBe(5);
    expect(3 * 4).toBe(12);
    expect(8 / 2).toBe(4);
  });

  it('should handle string operations', () => {
    expect('hello' + ' world').toBe('hello world');
    expect('test'.toUpperCase()).toBe('TEST');
    expect('TEST'.toLowerCase()).toBe('test');
  });

  it('should handle array operations', () => {
    const arr = [1, 2, 3];
    expect(arr.length).toBe(3);
    expect(arr.includes(2)).toBe(true);
    expect(arr.includes(4)).toBe(false);
  });

  it('should handle object operations', () => {
    const obj = { name: 'test', value: 42 };
    expect(obj.name).toBe('test');
    expect(obj.value).toBe(42);
    expect(Object.keys(obj)).toEqual(['name', 'value']);
  });

  it('should handle async operations', async () => {
    const promise = Promise.resolve('success');
    const result = await promise;
    expect(result).toBe('success');
  });

  it('should handle error cases', () => {
    expect(() => {
      throw new Error('test error');
    }).toThrow('test error');
  });
});

describe('Environment Test', () => {
  it('should have Node.js environment available', () => {
    expect(typeof process).toBe('object');
    expect(process.env).toBeDefined();
  });

  it('should have Jest globals available', () => {
    expect(typeof describe).toBe('function');
    expect(typeof it).toBe('function');
    expect(typeof expect).toBe('function');
    expect(typeof beforeAll).toBe('function');
    expect(typeof afterAll).toBe('function');
  });
});
