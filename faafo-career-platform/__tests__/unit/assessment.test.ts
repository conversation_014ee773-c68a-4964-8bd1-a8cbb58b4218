import { TestDatabase, generateAssessmentFormData } from '../utils/testHelpers';
import { testAssessments, testUsers } from '../fixtures/testData';

describe('Assessment Unit Tests', () => {
  let testDb: TestDatabase;
  let testUser: any;

  beforeAll(async () => {
    testDb = new TestDatabase();
  });

  beforeEach(async () => {
    await testDb.cleanup();
    testUser = await testDb.createTestUser(testUsers.validUser);
  });

  afterAll(async () => {
    await testDb.cleanup();
    await testDb.disconnect();
  });

  describe('Assessment Creation', () => {
    it('should create assessment with default values', async () => {
      const assessment = await testDb.createTestAssessment(testUser.id);
      
      expect(assessment).toHaveProperty('id');
      expect(assessment.userId).toBe(testUser.id);
      expect(assessment.status).toBe('IN_PROGRESS');
      expect(assessment.currentStep).toBe(1);
      expect(assessment.createdAt).toBeDefined();
      expect(assessment.updatedAt).toBeDefined();
      expect(assessment.completedAt).toBeNull();
    });

    it('should create assessment with custom values', async () => {
      const customData = {
        status: 'COMPLETED' as const,
        currentStep: 6,
        completedAt: new Date()
      };
      
      const assessment = await testDb.createTestAssessment(testUser.id, customData);
      
      expect(assessment.status).toBe('COMPLETED');
      expect(assessment.currentStep).toBe(6);
      expect(assessment.completedAt).toBeDefined();
    });

    it('should enforce user relationship', async () => {
      const invalidUserId = 'non-existent-user-id';
      
      await expect(testDb.createTestAssessment(invalidUserId)).rejects.toThrow();
    });
  });

  describe('Assessment Progress Tracking', () => {
    it('should track current step correctly', async () => {
      const assessment = await testDb.createTestAssessment(testUser.id, { currentStep: 3 });
      
      expect(assessment.currentStep).toBe(3);
      expect(assessment.currentStep).toBeGreaterThan(0);
      expect(assessment.currentStep).toBeLessThanOrEqual(6); // Assuming 6 steps max
    });

    it('should validate step boundaries', async () => {
      // Test negative step
      await expect(
        testDb.createTestAssessment(testUser.id, { currentStep: -1 })
      ).rejects.toThrow();
      
      // Test zero step (should be allowed as initial state)
      const zeroStepAssessment = await testDb.createTestAssessment(testUser.id, { currentStep: 0 });
      expect(zeroStepAssessment.currentStep).toBe(0);
    });

    it('should handle status transitions correctly', async () => {
      // Start with IN_PROGRESS
      const assessment = await testDb.createTestAssessment(testUser.id);
      expect(assessment.status).toBe('IN_PROGRESS');
      
      // Should be able to complete
      const completedAssessment = await testDb.createTestAssessment(testUser.id, {
        status: 'COMPLETED',
        completedAt: new Date()
      });
      expect(completedAssessment.status).toBe('COMPLETED');
      expect(completedAssessment.completedAt).toBeDefined();
    });
  });

  describe('Assessment Data Validation', () => {
    it('should validate form data structure', () => {
      const validFormData = generateAssessmentFormData();
      
      // Check required fields exist
      expect(validFormData).toHaveProperty('dissatisfaction_triggers');
      expect(validFormData).toHaveProperty('desired_outcomes_skill_a');
      expect(validFormData).toHaveProperty('work_environment_preference');
      expect(validFormData).toHaveProperty('risk_tolerance');
      expect(validFormData).toHaveProperty('learning_style');
      expect(validFormData).toHaveProperty('time_commitment');
    });

    it('should handle array fields correctly', () => {
      const formData = generateAssessmentFormData();
      
      expect(Array.isArray(formData.dissatisfaction_triggers)).toBe(true);
      expect(formData.dissatisfaction_triggers.length).toBeGreaterThan(0);
    });

    it('should validate enum values', () => {
      const validValues = {
        desired_outcomes_skill_a: ['low', 'medium', 'high'],
        work_environment_preference: ['office', 'remote', 'hybrid'],
        risk_tolerance: ['low', 'medium', 'high'],
        learning_style: ['visual', 'auditory', 'hands_on', 'reading'],
        time_commitment: ['5_10_hours', '10_15_hours', '15_20_hours', '20_plus_hours']
      };
      
      const formData = generateAssessmentFormData();
      
      Object.entries(validValues).forEach(([key, allowedValues]) => {
        if (formData[key]) {
          expect(allowedValues).toContain(formData[key]);
        }
      });
    });

    it('should handle invalid data gracefully', () => {
      const invalidData = {
        dissatisfaction_triggers: 'should_be_array', // Invalid: string instead of array
        desired_outcomes_skill_a: 'invalid_value',   // Invalid: not in enum
        work_environment_preference: null,           // Invalid: null value
        risk_tolerance: 123                          // Invalid: number instead of string
      };
      
      // The validation should happen at the API level
      // Here we just ensure the data structure is as expected
      expect(typeof invalidData.dissatisfaction_triggers).toBe('string');
      expect(invalidData.work_environment_preference).toBeNull();
      expect(typeof invalidData.risk_tolerance).toBe('number');
    });
  });

  describe('Assessment Scoring Logic', () => {
    it('should calculate scores based on responses', () => {
      const responses = testAssessments.completeAssessment;
      
      // Mock scoring algorithm
      const calculateSkillScore = (skillKey: string) => {
        const value = responses[skillKey as keyof typeof responses];
        const scoreMap = { low: 1, medium: 2, high: 3 };
        return scoreMap[value as keyof typeof scoreMap] || 0;
      };
      
      const skillAScore = calculateSkillScore('desired_outcomes_skill_a');
      const skillBScore = calculateSkillScore('desired_outcomes_skill_b');
      
      expect(skillAScore).toBe(3); // 'high' = 3
      expect(skillBScore).toBe(2); // 'medium' = 2
    });

    it('should handle missing responses', () => {
      const incompleteResponses = testAssessments.partialAssessment;
      
      const calculateSkillScore = (skillKey: string) => {
        const value = incompleteResponses[skillKey as keyof typeof incompleteResponses];
        if (!value) return 0;
        const scoreMap = { low: 1, medium: 2, high: 3 };
        return scoreMap[value as keyof typeof scoreMap] || 0;
      };
      
      const skillAScore = calculateSkillScore('desired_outcomes_skill_a');
      const missingSkillScore = calculateSkillScore('desired_outcomes_skill_b');
      
      expect(skillAScore).toBe(3);
      expect(missingSkillScore).toBe(0);
    });

    it('should weight different factors appropriately', () => {
      const responses = testAssessments.completeAssessment;
      
      // Mock weighted scoring
      const weights = {
        skill_preference: 0.4,
        work_environment: 0.2,
        risk_tolerance: 0.2,
        time_commitment: 0.2
      };
      
      const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
      expect(totalWeight).toBeCloseTo(1.0, 2);
    });
  });

  describe('Assessment Insights Generation', () => {
    it('should generate career path suggestions', () => {
      const responses = testAssessments.completeAssessment;
      
      // Mock insight generation
      const generateInsights = (data: typeof responses) => {
        const insights = [];
        
        if (data.desired_outcomes_skill_a === 'high') {
          insights.push('Strong interest in technical skills');
        }
        
        if (data.work_environment_preference === 'remote') {
          insights.push('Prefers remote work flexibility');
        }
        
        if (data.risk_tolerance === 'medium') {
          insights.push('Balanced approach to career risks');
        }
        
        return insights;
      };
      
      const insights = generateInsights(responses);
      
      expect(insights).toContain('Strong interest in technical skills');
      expect(insights).toContain('Prefers remote work flexibility');
      expect(insights).toContain('Balanced approach to career risks');
    });

    it('should provide actionable recommendations', () => {
      const responses = testAssessments.completeAssessment;
      
      // Mock recommendation engine
      const generateRecommendations = (data: typeof responses) => {
        const recommendations = [];
        
        if (data.learning_style === 'hands_on') {
          recommendations.push('Look for courses with practical labs and projects');
        }
        
        if (data.time_commitment === '10_15_hours') {
          recommendations.push('Consider part-time learning programs');
        }
        
        return recommendations;
      };
      
      const recommendations = generateRecommendations(responses);
      
      expect(recommendations.length).toBeGreaterThan(0);
      expect(recommendations[0]).toContain('practical labs');
    });
  });

  describe('Assessment Data Persistence', () => {
    it('should maintain data integrity over time', async () => {
      const assessment = await testDb.createTestAssessment(testUser.id);
      const createdTime = assessment.createdAt;
      
      // Simulate time passing
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // The created time should remain unchanged
      expect(assessment.createdAt).toEqual(createdTime);
    });

    it('should handle concurrent assessments per user', async () => {
      // A user should only have one active assessment
      const assessment1 = await testDb.createTestAssessment(testUser.id);
      
      // Creating another assessment for the same user should either:
      // 1. Replace the existing one, or
      // 2. Be rejected
      // This depends on business logic
      
      expect(assessment1.userId).toBe(testUser.id);
    });
  });
});
