import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { NextRequest, NextResponse } from 'next/server';

// Test database utilities
export class TestDatabase {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
        }
      }
    });
  }

  async cleanup() {
    // Clean up test data in reverse dependency order
    await this.prisma.resourceRating.deleteMany();
    await this.prisma.userLearningProgress.deleteMany();
    await this.prisma.assessmentResponse.deleteMany();
    await this.prisma.assessment.deleteMany();
    await this.prisma.freedomFund.deleteMany();
    await this.prisma.forumReply.deleteMany();
    await this.prisma.forumPost.deleteMany();
    await this.prisma.profile.deleteMany();
    await this.prisma.session.deleteMany();
    await this.prisma.account.deleteMany();
    await this.prisma.user.deleteMany();
  }

  async createTestUser(overrides: Partial<any> = {}) {
    const hashedPassword = await bcrypt.hash('testpassword123', 10);
    
    return await this.prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User',
        ...overrides
      }
    });
  }

  async createTestAssessment(userId: string, overrides: Partial<any> = {}) {
    return await this.prisma.assessment.create({
      data: {
        userId,
        status: 'IN_PROGRESS',
        currentStep: 1,
        ...overrides
      }
    });
  }

  async createTestLearningResource(overrides: Partial<any> = {}) {
    return await this.prisma.learningResource.create({
      data: {
        title: 'Test Resource',
        description: 'A test learning resource',
        url: 'https://example.com/test-resource',
        type: 'COURSE',
        category: 'CYBERSECURITY',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'SELF_PACED',
        ...overrides
      }
    });
  }

  async createTestProgress(userId: string, resourceId: string, overrides: Partial<any> = {}) {
    return await this.prisma.userLearningProgress.create({
      data: {
        userId,
        resourceId,
        status: 'IN_PROGRESS',
        ...overrides
      }
    });
  }

  async createTestRating(userId: string, resourceId: string, overrides: Partial<any> = {}) {
    return await this.prisma.resourceRating.create({
      data: {
        userId,
        resourceId,
        rating: 5,
        review: 'Great resource!',
        isHelpful: true,
        ...overrides
      }
    });
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }
}

// Mock session helper
export const createMockSession = (userId: string, userEmail: string = '<EMAIL>') => ({
  user: {
    id: userId,
    email: userEmail,
    name: 'Test User'
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
});

// API testing utilities
export class APITestHelper {
  static createMockRequest(
    method: string = 'GET',
    url: string = 'http://localhost:3000/api/test',
    body?: any,
    headers: Record<string, string> = {}
  ): NextRequest {
    const requestInit: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (body && method !== 'GET') {
      requestInit.body = JSON.stringify(body);
    }

    return new NextRequest(url, requestInit);
  }

  static async parseResponse(response: NextResponse) {
    const text = await response.text();
    try {
      return JSON.parse(text);
    } catch {
      return text;
    }
  }
}

// Form data generators
export const generateAssessmentFormData = (overrides: Record<string, any> = {}) => ({
  dissatisfaction_triggers: ['lack_of_growth', 'poor_work_life_balance'],
  desired_outcomes_skill_a: 'high',
  desired_outcomes_skill_b: 'medium',
  desired_outcomes_skill_c: 'low',
  work_environment_preference: 'remote',
  risk_tolerance: 'medium',
  learning_style: 'hands_on',
  time_commitment: '10_15_hours',
  ...overrides
});

// Component testing utilities
export const mockNextRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  pathname: '/',
  route: '/',
  query: {},
  asPath: '/',
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  }
};

// Mock fetch for API calls
export const createMockFetch = (responses: Array<{ url?: string; response: any; status?: number }>) => {
  return jest.fn().mockImplementation((url: string) => {
    const mockResponse = responses.find(r => !r.url || url.includes(r.url)) || responses[0];
    
    return Promise.resolve({
      ok: (mockResponse.status || 200) < 400,
      status: mockResponse.status || 200,
      json: () => Promise.resolve(mockResponse.response),
      text: () => Promise.resolve(JSON.stringify(mockResponse.response))
    });
  });
};

// Validation helpers
export const validateAPIResponse = (response: any, expectedFields: string[]) => {
  expectedFields.forEach(field => {
    expect(response).toHaveProperty(field);
  });
};

export const validateErrorResponse = (response: any, expectedStatus: number = 400) => {
  expect(response).toHaveProperty('error');
  expect(typeof response.error).toBe('string');
};

// Test data generators
export const generateTestUsers = (count: number = 3) => {
  return Array.from({ length: count }, (_, i) => ({
    email: `testuser${i + 1}@example.com`,
    password: 'testpassword123',
    name: `Test User ${i + 1}`
  }));
};

export const generateTestResources = (count: number = 5) => {
  const categories = ['CYBERSECURITY', 'DATA_SCIENCE', 'WEB_DEVELOPMENT'];
  const types = ['COURSE', 'ARTICLE', 'VIDEO'];
  const skillLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'];
  
  return Array.from({ length: count }, (_, i) => ({
    title: `Test Resource ${i + 1}`,
    description: `Description for test resource ${i + 1}`,
    url: `https://example.com/resource-${i + 1}`,
    type: types[i % types.length],
    category: categories[i % categories.length],
    skillLevel: skillLevels[i % skillLevels.length],
    cost: 'FREE',
    format: 'SELF_PACED'
  }));
};

// Performance testing utilities
export const measureExecutionTime = async (fn: () => Promise<any>) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  return {
    result,
    executionTime: end - start
  };
};

// Security testing utilities
export const generateMaliciousInputs = () => ({
  sqlInjection: ["'; DROP TABLE users; --", "1' OR '1'='1", "admin'--"],
  xss: ["<script>alert('xss')</script>", "javascript:alert('xss')", "<img src=x onerror=alert('xss')>"],
  pathTraversal: ["../../../etc/passwd", "..\\..\\..\\windows\\system32\\config\\sam"],
  oversizedInput: "A".repeat(10000),
  nullBytes: "test\x00.txt",
  specialChars: "!@#$%^&*()_+-=[]{}|;':\",./<>?"
});
