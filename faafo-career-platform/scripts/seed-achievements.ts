import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedAchievements() {
  console.log('🌱 Seeding achievements...');

  const achievements = [
    {
      title: 'First Steps',
      description: 'Complete your first learning resource',
      type: 'LEARNING_MILESTONE',
      icon: '🎯',
      criteria: { completedResources: 1 },
      points: 10,
    },
    {
      title: 'Learning Enthusiast',
      description: 'Complete 10 learning resources',
      type: 'LEARNING_MILESTONE',
      icon: '📚',
      criteria: { completedResources: 10 },
      points: 50,
    },
    {
      title: 'Knowledge Seeker',
      description: 'Complete 25 learning resources',
      type: 'LEARNING_MILESTONE',
      icon: '🔍',
      criteria: { completedResources: 25 },
      points: 100,
    },
    {
      title: 'Master Learner',
      description: 'Complete 50 learning resources',
      type: 'LEARNING_MILESTONE',
      icon: '🎓',
      criteria: { completedResources: 50 },
      points: 200,
    },
    {
      title: 'Community Helper',
      description: 'Make 5 forum posts or replies',
      type: 'COMMUNITY_CONTRIBUTOR',
      icon: '💬',
      criteria: { forumActivity: 5 },
      points: 25,
    },
    {
      title: 'Forum Regular',
      description: 'Make 20 forum posts or replies',
      type: 'COMMUNITY_CONTRIBUTOR',
      icon: '🗣️',
      criteria: { forumActivity: 20 },
      points: 75,
    },
    {
      title: 'Community Leader',
      description: 'Make 50 forum posts or replies',
      type: 'COMMUNITY_CONTRIBUTOR',
      icon: '👑',
      criteria: { forumActivity: 50 },
      points: 150,
    },
    {
      title: 'Goal Achiever',
      description: 'Complete your first goal',
      type: 'GOAL_ACHIEVER',
      icon: '✅',
      criteria: { completedGoals: 1 },
      points: 20,
    },
    {
      title: 'Goal Master',
      description: 'Complete 5 goals',
      type: 'GOAL_ACHIEVER',
      icon: '🏆',
      criteria: { completedGoals: 5 },
      points: 75,
    },
    {
      title: 'Dedicated Learner',
      description: 'Maintain a 7-day learning streak',
      type: 'STREAK_ACHIEVEMENT',
      icon: '🔥',
      criteria: { streakDays: 7 },
      points: 30,
    },
    {
      title: 'Consistency Champion',
      description: 'Maintain a 30-day learning streak',
      type: 'STREAK_ACHIEVEMENT',
      icon: '💪',
      criteria: { streakDays: 30 },
      points: 100,
    },
    {
      title: 'Reviewer',
      description: 'Rate 5 learning resources',
      type: 'COMPLETION_BADGE',
      icon: '⭐',
      criteria: { ratings: 5 },
      points: 15,
    },
    {
      title: 'Critic',
      description: 'Rate 20 learning resources',
      type: 'COMPLETION_BADGE',
      icon: '📝',
      criteria: { ratings: 20 },
      points: 50,
    },
    {
      title: 'Skill Explorer',
      description: 'Complete resources in 3 different categories',
      type: 'SKILL_MASTER',
      icon: '🌟',
      criteria: { categoriesExplored: 3 },
      points: 40,
    },
    {
      title: 'Renaissance Learner',
      description: 'Complete resources in 6 different categories',
      type: 'SKILL_MASTER',
      icon: '🌈',
      criteria: { categoriesExplored: 6 },
      points: 100,
    },
  ];

  for (const achievement of achievements) {
    try {
      await prisma.achievement.upsert({
        where: { title: achievement.title },
        update: achievement,
        create: achievement,
      });
      console.log(`✅ Created/updated achievement: ${achievement.title}`);
    } catch (error) {
      console.error(`❌ Error creating achievement ${achievement.title}:`, error);
    }
  }

  console.log('🎉 Achievement seeding completed!');
}

async function main() {
  try {
    await seedAchievements();
  } catch (error) {
    console.error('Error seeding achievements:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export default main;
