import prisma from "../src/lib/prisma";

async function testCrudOperations() {
  try {
    // Create a new user
    const newUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        password: "password123", // In a real app, password should be hashed
      },
    });
    console.log("Created user:", newUser);

    // Read the user
    const user = await prisma.user.findUnique({
      where: {
        id: newUser.id,
      },
    });
    console.log("Read user:", user);

    // Update the user
    const updatedUser = await prisma.user.update({
      where: {
        id: newUser.id,
      },
      data: {
        email: "<EMAIL>",
      },
    });
    console.log("Updated user:", updatedUser);

    // Delete the user
    const deletedUser = await prisma.user.delete({
      where: {
        id: newUser.id,
      },
    });
    console.log("Deleted user:", deletedUser);

  } catch (error) {
    console.error("Error performing CRUD operations:", error);
  } finally {
    await prisma.$disconnect();
  }
}

testCrudOperations(); 