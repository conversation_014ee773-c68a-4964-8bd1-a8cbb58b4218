import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testForumImprovements() {
  console.log('🧪 Testing Forum Improvements...\n');

  try {
    // Test 1: Categories
    console.log('1. Testing Forum Categories...');
    const categories = await prisma.forumCategory.findMany({
      include: {
        children: true,
        _count: {
          select: {
            posts: true,
          },
        },
      },
    });
    console.log(`   ✅ Found ${categories.length} categories`);
    console.log(`   ✅ Categories with subcategories: ${categories.filter(c => c.children.length > 0).length}`);

    // Test 2: Create a test post with category
    console.log('\n2. Testing Post Creation with Category...');
    const firstUser = await prisma.user.findFirst();
    const firstCategory = categories[0];
    
    if (firstUser && firstCategory) {
      const testPost = await prisma.forumPost.create({
        data: {
          title: 'Test Post for Forum Improvements',
          content: 'This is a test post to verify the new forum features are working correctly.',
          authorId: firstUser.id,
          categoryId: firstCategory.id,
        },
        include: {
          author: {
            include: {
              profile: true,
            },
          },
          category: true,
          _count: {
            select: {
              replies: true,
              reactions: true,
              bookmarks: true,
            },
          },
        },
      });
      console.log(`   ✅ Created test post: "${testPost.title}" in category "${testPost.category?.name}"`);

      // Test 3: Reactions
      console.log('\n3. Testing Post Reactions...');
      const reaction = await prisma.forumPostReaction.create({
        data: {
          userId: firstUser.id,
          postId: testPost.id,
          type: 'LIKE',
        },
      });
      console.log(`   ✅ Created reaction: ${reaction.type}`);

      // Update post like count
      await prisma.forumPost.update({
        where: { id: testPost.id },
        data: { likeCount: { increment: 1 } },
      });

      // Test 4: Bookmarks
      console.log('\n4. Testing Post Bookmarks...');
      const bookmark = await prisma.forumBookmark.create({
        data: {
          userId: firstUser.id,
          postId: testPost.id,
        },
      });
      console.log(`   ✅ Created bookmark for post`);

      // Test 5: User Profile Updates
      console.log('\n5. Testing User Profile Updates...');
      await prisma.profile.upsert({
        where: { userId: firstUser.id },
        update: {
          forumPostCount: { increment: 1 },
          forumReputation: { increment: 1 },
          currentCareerPath: 'Technology',
          progressLevel: 'Beginner',
        },
        create: {
          userId: firstUser.id,
          forumPostCount: 1,
          forumReputation: 1,
          currentCareerPath: 'Technology',
          progressLevel: 'Beginner',
        },
      });
      console.log(`   ✅ Updated user profile with forum stats`);

      // Test 6: Moderation
      console.log('\n6. Testing Moderation Features...');
      const moderators = await prisma.forumModerator.findMany({
        include: {
          user: true,
          category: true,
        },
      });
      console.log(`   ✅ Found ${moderators.length} moderators`);

      // Test 7: Reports
      console.log('\n7. Testing Report System...');
      const report = await prisma.forumReport.create({
        data: {
          reporterId: firstUser.id,
          postId: testPost.id,
          reason: 'OTHER',
          description: 'Test report for verification',
        },
      });
      console.log(`   ✅ Created test report`);

      // Test 8: Category Statistics Update
      console.log('\n8. Testing Category Statistics...');
      await prisma.forumCategory.update({
        where: { id: firstCategory.id },
        data: {
          postCount: { increment: 1 },
          lastPostAt: new Date(),
          lastPostBy: firstUser.id,
        },
      });
      console.log(`   ✅ Updated category statistics`);

      // Test 9: Enhanced Post Queries
      console.log('\n9. Testing Enhanced Post Queries...');
      const enhancedPosts = await prisma.forumPost.findMany({
        where: {
          isHidden: false,
        },
        include: {
          author: {
            select: {
              id: true,
              email: true,
              name: true,
              profile: {
                select: {
                  profilePictureUrl: true,
                  forumReputation: true,
                  forumPostCount: true,
                  currentCareerPath: true,
                  progressLevel: true,
                },
              },
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              replies: true,
              reactions: true,
              bookmarks: true,
            },
          },
          reactions: {
            select: {
              type: true,
              userId: true,
            },
          },
        },
        orderBy: [
          { isPinned: 'desc' },
          { createdAt: 'desc' },
        ],
      });
      console.log(`   ✅ Retrieved ${enhancedPosts.length} posts with enhanced data`);

      // Test 10: Cleanup
      console.log('\n10. Cleaning up test data...');
      await prisma.forumReport.delete({ where: { id: report.id } });
      await prisma.forumBookmark.delete({ where: { id: bookmark.id } });
      await prisma.forumPostReaction.delete({ where: { id: reaction.id } });
      await prisma.forumPost.delete({ where: { id: testPost.id } });
      console.log(`   ✅ Cleaned up test data`);
    }

    // Test 11: API Endpoint Verification
    console.log('\n11. Verifying Database Schema...');
    const schemaInfo = {
      categories: await prisma.forumCategory.count(),
      posts: await prisma.forumPost.count(),
      replies: await prisma.forumReply.count(),
      reactions: await prisma.forumPostReaction.count(),
      bookmarks: await prisma.forumBookmark.count(),
      moderators: await prisma.forumModerator.count(),
      reports: await prisma.forumReport.count(),
    };
    
    console.log('   📊 Database Statistics:');
    Object.entries(schemaInfo).forEach(([key, value]) => {
      console.log(`      ${key}: ${value}`);
    });

    console.log('\n🎉 All Forum Improvements Tests Passed!');
    console.log('\n📋 Summary of Implemented Features:');
    console.log('   ✅ Hierarchical forum categories with statistics');
    console.log('   ✅ Enhanced user profiles with reputation and career info');
    console.log('   ✅ Post reactions system (like, helpful, insightful, etc.)');
    console.log('   ✅ Bookmark functionality for posts');
    console.log('   ✅ Comprehensive moderation system');
    console.log('   ✅ Report system for inappropriate content');
    console.log('   ✅ Enhanced post queries with engagement metrics');
    console.log('   ✅ Category-based post filtering');
    console.log('   ✅ User reputation and achievement tracking');

  } catch (error) {
    console.error('❌ Error during testing:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test function if this file is executed directly
if (require.main === module) {
  testForumImprovements()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export default testForumImprovements;
