import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedForumModerators() {
  console.log('🌱 Seeding forum moderators...');

  try {
    // Find the first user to make them a super admin
    const firstUser = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'asc',
      },
    });

    if (!firstUser) {
      console.log('No users found. Please create a user first.');
      return;
    }

    // Check if super admin already exists
    const existingSuperAdmin = await prisma.forumModerator.findFirst({
      where: {
        userId: firstUser.id,
        categoryId: null,
        role: 'SUPER_ADMIN',
      },
    });

    if (!existingSuperAdmin) {
      const superAdmin = await prisma.forumModerator.create({
        data: {
          userId: firstUser.id,
          categoryId: null,
          role: 'SUPER_ADMIN',
          permissions: {
            canModerateAllCategories: true,
            canManageCategories: true,
            canManageModerators: true,
            canViewReports: true,
            canResolveReports: true,
            canHidePosts: true,
            canLockPosts: true,
            canDeletePosts: true,
            canBanUsers: true,
            canPinPosts: true,
          },
          assignedBy: firstUser.id,
          isActive: true,
        },
      });
      console.log(`✅ Created super admin moderator for user: ${firstUser.email}`);
    } else {
      console.log(`✅ Super admin moderator already exists for user: ${firstUser.email}`);
    }

    console.log(`✅ Created super admin moderator for user: ${firstUser.email}`);

    // Get some categories to assign moderators to
    const categories = await prisma.forumCategory.findMany({
      where: {
        parentId: null, // Only main categories
      },
      take: 3,
    });

    // Find additional users to make category moderators
    const additionalUsers = await prisma.user.findMany({
      where: {
        id: {
          not: firstUser.id,
        },
      },
      take: 3,
    });

    // Create category moderators if we have additional users
    for (let i = 0; i < Math.min(additionalUsers.length, categories.length); i++) {
      const user = additionalUsers[i];
      const category = categories[i];

      // Check if moderator already exists
      const existingModerator = await prisma.forumModerator.findFirst({
        where: {
          userId: user.id,
          categoryId: category.id,
        },
      });

      if (!existingModerator) {
        await prisma.forumModerator.create({
          data: {
            userId: user.id,
            categoryId: category.id,
            role: 'MODERATOR',
            permissions: {
              canModerateCategory: true,
              canViewReports: true,
              canResolveReports: true,
              canHidePosts: true,
              canLockPosts: true,
              canPinPosts: true,
            },
            assignedBy: firstUser.id,
            isActive: true,
          },
        });
        console.log(`✅ Created category moderator for ${user.email} in ${category.name}`);
      } else {
        console.log(`✅ Category moderator already exists for ${user.email} in ${category.name}`);
      }
    }

    console.log('✅ Forum moderators seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding forum moderators:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedForumModerators()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export default seedForumModerators;
