// Test script to verify all API endpoints work correctly
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000';

interface TestResult {
  endpoint: string;
  method: string;
  status: number;
  success: boolean;
  error?: string;
  data?: any;
}

class APITester {
  private results: TestResult[] = [];
  private sessionCookie: string = '';

  async testEndpoint(endpoint: string, method: string = 'GET', body?: any, requiresAuth: boolean = false): Promise<TestResult> {
    try {
      const headers: any = {
        'Content-Type': 'application/json',
      };

      if (requiresAuth && this.sessionCookie) {
        headers['Cookie'] = this.sessionCookie;
      }

      const options: any = {
        method,
        headers,
      };

      if (body && method !== 'GET') {
        options.body = JSON.stringify(body);
      }

      const response = await fetch(`${BASE_URL}${endpoint}`, options);
      const data = await response.json();

      const result: TestResult = {
        endpoint,
        method,
        status: response.status,
        success: response.ok,
        data
      };

      if (!response.ok) {
        result.error = data.error || data.message || 'Unknown error';
      }

      this.results.push(result);
      return result;
    } catch (error) {
      const result: TestResult = {
        endpoint,
        method,
        status: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      this.results.push(result);
      return result;
    }
  }

  async authenticateTestUser(): Promise<boolean> {
    console.log('🔐 Authenticating test user...');
    
    try {
      // First get CSRF token
      const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf`);
      const csrfData = await csrfResponse.json();
      
      // Then attempt login
      const loginResponse = await fetch(`${BASE_URL}/api/auth/callback/credentials`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          email: '<EMAIL>',
          password: 'testpassword',
          csrfToken: csrfData.csrfToken,
          callbackUrl: `${BASE_URL}/dashboard`,
          json: 'true'
        }),
        redirect: 'manual'
      });

      if (loginResponse.status === 200 || loginResponse.status === 302) {
        const cookies = loginResponse.headers.get('set-cookie');
        if (cookies) {
          this.sessionCookie = cookies;
          console.log('✅ Authentication successful');
          return true;
        }
      }
      
      console.log('❌ Authentication failed');
      return false;
    } catch (error) {
      console.error('❌ Authentication error:', error);
      return false;
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting API endpoint tests...\n');

    // Test public endpoints first
    console.log('📋 Testing public endpoints...');
    await this.testEndpoint('/api/auth/session');
    await this.testEndpoint('/api/auth/providers');
    await this.testEndpoint('/api/auth/csrf');

    // Authenticate
    const authSuccess = await this.authenticateTestUser();
    
    if (authSuccess) {
      console.log('\n📋 Testing authenticated endpoints...');
      
      // Test dashboard-related endpoints
      await this.testEndpoint('/api/assessment', 'GET', null, true);
      await this.testEndpoint('/api/freedom-fund', 'GET', null, true);
      await this.testEndpoint('/api/forum/posts', 'GET', null, true);
      await this.testEndpoint('/api/personalized-resources?limit=6', 'GET', null, true);
      await this.testEndpoint('/api/profile', 'GET', null, true);
      await this.testEndpoint('/api/career-paths', 'GET', null, true);
      await this.testEndpoint('/api/learning-resources', 'GET', null, true);
      
      // Test learning progress endpoints
      await this.testEndpoint('/api/learning-progress', 'GET', null, true);
      
      // Test resource ratings
      await this.testEndpoint('/api/resource-ratings', 'GET', null, true);
    }

    this.printResults();
  }

  printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('=' .repeat(80));
    
    let passed = 0;
    let failed = 0;

    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const statusCode = result.status || 'ERR';
      console.log(`${status} ${result.method.padEnd(4)} ${result.endpoint.padEnd(40)} [${statusCode}]`);
      
      if (result.error) {
        console.log(`    Error: ${result.error}`);
      }
      
      if (result.success) {
        passed++;
      } else {
        failed++;
      }
    });

    console.log('=' .repeat(80));
    console.log(`Total: ${this.results.length} | Passed: ${passed} | Failed: ${failed}`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed!');
    } else {
      console.log(`⚠️  ${failed} test(s) failed`);
    }
  }
}

async function main() {
  const tester = new APITester();
  await tester.runAllTests();
}

main().catch(console.error);
