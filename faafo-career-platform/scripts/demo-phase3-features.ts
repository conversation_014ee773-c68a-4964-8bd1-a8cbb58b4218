#!/usr/bin/env tsx

/**
 * Demo script for Phase 3: Career Path Enhancements
 * Demonstrates bookmark functionality, action plan tracking, and enhanced recommendations
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createDemoUser() {
  console.log('👤 Creating demo user...');
  
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Demo User'
    }
  });

  console.log(`✅ Demo user created: ${demoUser.name} (${demoUser.email})`);
  return demoUser;
}

async function demonstrateBookmarks(userId: string) {
  console.log('\n📌 Demonstrating Career Path Bookmarking...');
  
  // Get available career paths
  const careerPaths = await prisma.careerPath.findMany({
    where: { isActive: true },
    take: 3
  });

  console.log(`Found ${careerPaths.length} career paths to bookmark`);

  // Create bookmarks
  for (const careerPath of careerPaths) {
    const bookmark = await prisma.careerPathBookmark.create({
      data: {
        userId: userId,
        careerPathId: careerPath.id,
        notes: `Interested in ${careerPath.name} - looks promising for my career transition!`
      }
    });

    console.log(`  ✅ Bookmarked: ${careerPath.name}`);
  }

  // Retrieve user's bookmarks
  const userBookmarks = await prisma.careerPathBookmark.findMany({
    where: { userId },
    include: {
      careerPath: {
        select: {
          name: true,
          overview: true
        }
      }
    }
  });

  console.log(`\n📋 User has ${userBookmarks.length} bookmarked career paths:`);
  userBookmarks.forEach((bookmark, index) => {
    console.log(`  ${index + 1}. ${bookmark.careerPath.name}`);
    console.log(`     Notes: ${bookmark.notes}`);
  });

  return userBookmarks;
}

async function demonstrateActionPlanProgress(userId: string, careerPathId: string) {
  console.log('\n📋 Demonstrating Action Plan Progress Tracking...');

  const careerPath = await prisma.careerPath.findUnique({
    where: { id: careerPathId }
  });

  if (!careerPath) {
    console.log('❌ Career path not found');
    return;
  }

  console.log(`Tracking progress for: ${careerPath.name}`);

  // Parse action steps
  const actionSteps = Array.isArray(careerPath.actionableSteps)
    ? careerPath.actionableSteps
    : JSON.parse(careerPath.actionableSteps as string);

  console.log(`Found ${actionSteps.length} action steps`);

  // Create progress entries for first few steps
  const progressEntries = [];
  for (let i = 0; i < Math.min(3, actionSteps.length); i++) {
    const isCompleted = Math.random() > 0.5; // Randomly complete some steps
    
    const progress = await prisma.actionPlanProgress.create({
      data: {
        userId: userId,
        careerPathId: careerPathId,
        stepIndex: i,
        stepTitle: actionSteps[i],
        isCompleted: isCompleted,
        completedAt: isCompleted ? new Date() : null,
        notes: isCompleted ? `Completed step ${i + 1}` : `Working on step ${i + 1}`
      }
    });

    progressEntries.push(progress);
    console.log(`  ${isCompleted ? '✅' : '⏳'} Step ${i + 1}: ${actionSteps[i]}`);
  }

  // Calculate progress percentage
  const completedSteps = progressEntries.filter(p => p.isCompleted).length;
  const totalSteps = progressEntries.length;
  const progressPercentage = Math.round((completedSteps / totalSteps) * 100);

  console.log(`\n📊 Progress Summary:`);
  console.log(`  Completed: ${completedSteps}/${totalSteps} steps`);
  console.log(`  Progress: ${progressPercentage}%`);

  return progressEntries;
}

async function demonstrateEnhancedRecommendations(userId: string) {
  console.log('\n🎯 Demonstrating Enhanced Recommendations...');

  // Get user's bookmarked career paths
  const bookmarkedPaths = await prisma.careerPathBookmark.findMany({
    where: { userId },
    include: { careerPath: true }
  });

  console.log(`User has bookmarked ${bookmarkedPaths.length} career paths`);

  // Get all career paths with related data for recommendations
  const allCareerPaths = await prisma.careerPath.findMany({
    where: { isActive: true },
    include: {
      relatedSkills: true,
      relatedIndustries: true,
      bookmarks: true,
      suggestionRules: true
    }
  });

  // Simple recommendation algorithm based on bookmarked paths
  const recommendations = [];
  
  for (const careerPath of allCareerPaths) {
    // Skip already bookmarked paths
    if (bookmarkedPaths.some(b => b.careerPathId === careerPath.id)) {
      continue;
    }

    let score = 0;
    let reasons = [];

    // Score based on popularity (number of bookmarks)
    const popularityScore = careerPath.bookmarks.length * 0.5;
    score += popularityScore;
    if (popularityScore > 0) {
      reasons.push(`Popular among users (${careerPath.bookmarks.length} bookmarks)`);
    }

    // Score based on skill similarity to bookmarked paths
    for (const bookmark of bookmarkedPaths) {
      const commonSkills = careerPath.relatedSkills.filter(skill =>
        bookmark.careerPath.relatedSkills?.some((s: any) => s.id === skill.id)
      );
      
      if (commonSkills.length > 0) {
        score += commonSkills.length * 2;
        reasons.push(`Shares ${commonSkills.length} skills with ${bookmark.careerPath.name}`);
      }
    }

    if (score > 0) {
      recommendations.push({
        careerPath,
        score,
        reasons,
        recommendationType: score > 3 ? 'skill_based' : 'trending'
      });
    }
  }

  // Sort by score and take top 3
  recommendations.sort((a, b) => b.score - a.score);
  const topRecommendations = recommendations.slice(0, 3);

  console.log(`\n🌟 Top ${topRecommendations.length} Recommendations:`);
  topRecommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec.careerPath.name} (Score: ${rec.score.toFixed(1)})`);
    console.log(`     Type: ${rec.recommendationType}`);
    console.log(`     Reasons: ${rec.reasons.join(', ')}`);
    console.log(`     Skills: ${rec.careerPath.relatedSkills.map((s: any) => s.name).join(', ')}`);
  });

  return topRecommendations;
}

async function demonstrateIntegration(userId: string) {
  console.log('\n🔗 Demonstrating Feature Integration...');

  // Get comprehensive user data
  const userData = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      careerPathBookmarks: {
        include: {
          careerPath: {
            select: {
              id: true,
              name: true,
              overview: true
            }
          }
        }
      },
      actionPlanProgress: {
        include: {
          careerPath: {
            select: {
              name: true
            }
          }
        }
      }
    }
  });

  if (!userData) {
    console.log('❌ User not found');
    return;
  }

  console.log(`\n👤 User Profile: ${userData.name}`);
  console.log(`📧 Email: ${userData.email}`);
  console.log(`📅 Member since: ${userData.createdAt.toDateString()}`);

  console.log(`\n📊 Activity Summary:`);
  console.log(`  📌 Bookmarked Paths: ${userData.careerPathBookmarks.length}`);
  console.log(`  📋 Active Progress: ${userData.actionPlanProgress.length} steps tracked`);

  // Calculate overall progress
  const completedSteps = userData.actionPlanProgress.filter(p => p.isCompleted).length;
  const totalSteps = userData.actionPlanProgress.length;
  const overallProgress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

  console.log(`  ✅ Overall Progress: ${overallProgress}% (${completedSteps}/${totalSteps} steps completed)`);

  // Show career paths with progress
  const pathProgress = new Map();
  userData.actionPlanProgress.forEach(progress => {
    const pathName = progress.careerPath.name;
    if (!pathProgress.has(pathName)) {
      pathProgress.set(pathName, { completed: 0, total: 0 });
    }
    const current = pathProgress.get(pathName);
    current.total++;
    if (progress.isCompleted) current.completed++;
  });

  if (pathProgress.size > 0) {
    console.log(`\n📈 Progress by Career Path:`);
    pathProgress.forEach((progress, pathName) => {
      const percentage = Math.round((progress.completed / progress.total) * 100);
      console.log(`  ${pathName}: ${percentage}% (${progress.completed}/${progress.total})`);
    });
  }
}

async function cleanupDemo(userId: string) {
  console.log('\n🧹 Cleaning up demo data...');

  // Remove demo user's bookmarks and progress
  await prisma.careerPathBookmark.deleteMany({
    where: { userId }
  });

  await prisma.actionPlanProgress.deleteMany({
    where: { userId }
  });

  await prisma.user.delete({
    where: { id: userId }
  });

  console.log('✅ Demo data cleaned up');
}

async function main() {
  console.log('🚀 Phase 3: Career Path Enhancements Demo\n');
  console.log('=' .repeat(60));

  try {
    // Create demo user
    const demoUser = await createDemoUser();

    // Demonstrate bookmarking
    const bookmarks = await demonstrateBookmarks(demoUser.id);

    // Demonstrate action plan progress (use first bookmarked career path)
    if (bookmarks.length > 0) {
      await demonstrateActionPlanProgress(demoUser.id, bookmarks[0].careerPathId);
    }

    // Demonstrate enhanced recommendations
    await demonstrateEnhancedRecommendations(demoUser.id);

    // Show integration
    await demonstrateIntegration(demoUser.id);

    // Cleanup
    await cleanupDemo(demoUser.id);

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 Demo completed successfully!');
    console.log('\n✨ Phase 3 Features Demonstrated:');
    console.log('  📌 Career Path Bookmarking with notes');
    console.log('  📋 Action Plan Progress Tracking');
    console.log('  🎯 Enhanced Recommendation Engine');
    console.log('  🔗 Seamless Feature Integration');
    console.log('  📊 Comprehensive Progress Analytics');

  } catch (error) {
    console.error('💥 Demo failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch((error) => {
  console.error('💥 Demo execution failed:', error);
  process.exit(1);
});
