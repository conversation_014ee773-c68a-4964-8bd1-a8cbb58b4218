import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDashboardFlows() {
  console.log('🧪 Testing Dashboard Flows...\n');

  try {
    // 1. Verify test user exists
    console.log('1️⃣ Verifying test user...');
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      console.log('❌ Test user not found. Creating test user...');
      // Create test user if not exists
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('testpassword', 10);
      
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Test User',
          emailVerified: new Date(),
        },
      });
      
      console.log(`✅ Test user created: ${newUser.email}`);
    } else {
      console.log(`✅ Test user exists: ${testUser.email}`);
    }

    // 2. Check learning resources exist
    console.log('\n2️⃣ Checking learning resources...');
    const resourceCount = await prisma.learningResource.count();
    console.log(`✅ Found ${resourceCount} learning resources`);

    if (resourceCount === 0) {
      console.log('⚠️  No learning resources found. Run: npx tsx prisma/seed-learning-resources.ts');
    }

    // 3. Check career paths exist
    console.log('\n3️⃣ Checking career paths...');
    const careerPathCount = await prisma.careerPath.count();
    console.log(`✅ Found ${careerPathCount} career paths`);

    // 4. Test assessment flow
    console.log('\n4️⃣ Testing assessment flow...');
    
    // Check if test user has any assessments
    const existingAssessment = await prisma.assessment.findFirst({
      where: { userId: testUser!.id },
      include: { responses: true }
    });

    if (existingAssessment) {
      console.log(`✅ Found existing assessment: ${existingAssessment.status}`);
      console.log(`   - Current step: ${existingAssessment.currentStep}`);
      console.log(`   - Responses: ${existingAssessment.responses.length}`);
    } else {
      console.log('ℹ️  No assessment found for test user');
    }

    // 5. Test freedom fund
    console.log('\n5️⃣ Testing freedom fund...');
    const freedomFund = await prisma.freedomFund.findUnique({
      where: { userId: testUser!.id }
    });

    if (freedomFund) {
      console.log(`✅ Found freedom fund data:`);
      console.log(`   - Target: $${freedomFund.targetSavings}`);
      console.log(`   - Current: $${freedomFund.currentSavingsAmount || 0}`);
    } else {
      console.log('ℹ️  No freedom fund data found for test user');
    }

    // 6. Test forum posts
    console.log('\n6️⃣ Testing forum posts...');
    const forumPostCount = await prisma.forumPost.count({
      where: { authorId: testUser!.id }
    });
    console.log(`✅ Found ${forumPostCount} forum posts by test user`);

    // 7. Test learning progress
    console.log('\n7️⃣ Testing learning progress...');
    const progressCount = await prisma.userLearningProgress.count({
      where: { userId: testUser!.id }
    });
    console.log(`✅ Found ${progressCount} learning progress records`);

    // 8. Test resource ratings
    console.log('\n8️⃣ Testing resource ratings...');
    const ratingCount = await prisma.resourceRating.count({
      where: { userId: testUser!.id }
    });
    console.log(`✅ Found ${ratingCount} resource ratings by test user`);

    console.log('\n🎉 Dashboard flow test completed!');
    console.log('\n📋 Test Summary:');
    console.log(`   - Test user: ${testUser!.email}`);
    console.log(`   - Learning resources: ${resourceCount}`);
    console.log(`   - Career paths: ${careerPathCount}`);
    console.log(`   - Assessment: ${existingAssessment ? existingAssessment.status : 'None'}`);
    console.log(`   - Freedom fund: ${freedomFund ? 'Set' : 'Not set'}`);
    console.log(`   - Forum posts: ${forumPostCount}`);
    console.log(`   - Learning progress: ${progressCount}`);
    console.log(`   - Resource ratings: ${ratingCount}`);

    console.log('\n🔑 Test Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: testpassword');

  } catch (error) {
    console.error('❌ Error during dashboard flow test:', error);
    throw error;
  }
}

async function main() {
  await testDashboardFlows();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
