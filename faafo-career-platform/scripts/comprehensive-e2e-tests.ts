#!/usr/bin/env tsx

/**
 * Comprehensive End-to-End Testing Suite
 * Tests all critical aspects of the FAAFO Career Platform
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import fetch from 'node-fetch';

const prisma = new PrismaClient();
const BASE_URL = 'http://localhost:3000';

interface TestResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  details: string;
  duration?: number;
  error?: string;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
  };
}

class ComprehensiveE2ETester {
  private results: TestSuite[] = [];
  private testUser: any = null;
  private sessionCookie: string = '';

  constructor() {
    console.log('🚀 Initializing Comprehensive E2E Testing Suite');
    console.log('📋 Testing Coverage:');
    console.log('   • Core User Flows');
    console.log('   • Technical Areas');
    console.log('   • Edge Cases & Error Handling');
    console.log('   • Performance Testing');
    console.log('   • Security Testing');
    console.log('');
  }

  // ==================== UTILITY METHODS ====================

  private async makeRequest(endpoint: string, options: any = {}): Promise<any> {
    const startTime = Date.now();
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...(this.sessionCookie && { 'Cookie': this.sessionCookie }),
          ...options.headers
        },
        ...options
      });

      const duration = Date.now() - startTime;
      const data = await response.json().catch(() => ({}));

      return {
        status: response.status,
        ok: response.ok,
        data,
        duration,
        headers: response.headers
      };
    } catch (error) {
      return {
        status: 0,
        ok: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      };
    }
  }

  private createTestResult(
    category: string,
    test: string,
    status: 'PASS' | 'FAIL' | 'SKIP',
    details: string,
    duration?: number,
    error?: string
  ): TestResult {
    return { category, test, status, details, duration, error };
  }

  private addTestResult(result: TestResult): void {
    let suite = this.results.find(s => s.name === result.category);
    if (!suite) {
      suite = {
        name: result.category,
        tests: [],
        summary: { total: 0, passed: 0, failed: 0, skipped: 0 }
      };
      this.results.push(suite);
    }

    suite.tests.push(result);
    suite.summary.total++;
    
    switch (result.status) {
      case 'PASS': suite.summary.passed++; break;
      case 'FAIL': suite.summary.failed++; break;
      case 'SKIP': suite.summary.skipped++; break;
    }
  }

  // ==================== CORE USER FLOWS ====================

  async testUserRegistrationFlow(): Promise<void> {
    console.log('👤 Testing User Registration Flow...');
    
    // Test 1: User Registration
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    
    const registrationResult = await this.makeRequest('/api/signup', {
      method: 'POST',
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        name: 'Test User'
      })
    });

    if (registrationResult.ok) {
      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'User Registration',
        'PASS',
        `Successfully registered user: ${testEmail}`,
        registrationResult.duration
      ));
      
      // Store test user for subsequent tests
      this.testUser = { email: testEmail, password: testPassword };
    } else {
      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'User Registration',
        'FAIL',
        `Registration failed: ${registrationResult.data?.error || 'Unknown error'}`,
        registrationResult.duration,
        registrationResult.error
      ));
    }

    // Test 2: Duplicate Registration Prevention
    const duplicateResult = await this.makeRequest('/api/signup', {
      method: 'POST',
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        name: 'Duplicate User'
      })
    });

    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Duplicate Registration Prevention',
      !duplicateResult.ok ? 'PASS' : 'FAIL',
      !duplicateResult.ok 
        ? 'Correctly prevented duplicate registration'
        : 'Failed to prevent duplicate registration',
      duplicateResult.duration
    ));
  }

  async testAuthenticationFlow(): Promise<void> {
    console.log('🔐 Testing Authentication Flow...');
    
    if (!this.testUser) {
      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'Authentication',
        'SKIP',
        'No test user available for authentication testing'
      ));
      return;
    }

    // Test 1: Valid Login
    const loginResult = await this.makeRequest('/api/auth/callback/credentials', {
      method: 'POST',
      body: JSON.stringify({
        email: this.testUser.email,
        password: this.testUser.password
      })
    });

    if (loginResult.ok) {
      // Extract session cookie if available
      const setCookieHeader = loginResult.headers.get('set-cookie');
      if (setCookieHeader) {
        this.sessionCookie = setCookieHeader;
      }

      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'Valid Login',
        'PASS',
        'Successfully authenticated user',
        loginResult.duration
      ));
    } else {
      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'Valid Login',
        'FAIL',
        `Login failed: ${loginResult.data?.error || 'Unknown error'}`,
        loginResult.duration
      ));
    }

    // Test 2: Invalid Login
    const invalidLoginResult = await this.makeRequest('/api/auth/callback/credentials', {
      method: 'POST',
      body: JSON.stringify({
        email: this.testUser.email,
        password: 'wrongpassword'
      })
    });

    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Invalid Login Prevention',
      !invalidLoginResult.ok ? 'PASS' : 'FAIL',
      !invalidLoginResult.ok 
        ? 'Correctly rejected invalid credentials'
        : 'Failed to reject invalid credentials',
      invalidLoginResult.duration
    ));

    // Test 3: Session Validation
    const sessionResult = await this.makeRequest('/api/auth/session');
    
    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Session Validation',
      sessionResult.ok ? 'PASS' : 'FAIL',
      sessionResult.ok 
        ? 'Session validation working'
        : 'Session validation failed',
      sessionResult.duration
    ));
  }

  async testProfileManagement(): Promise<void> {
    console.log('👤 Testing Profile Management...');
    
    // Test 1: Profile Access
    const profileResult = await this.makeRequest('/api/profile');
    
    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Profile Access',
      profileResult.ok ? 'PASS' : 'FAIL',
      profileResult.ok 
        ? 'Profile data accessible'
        : `Profile access failed: ${profileResult.data?.error || 'Unknown error'}`,
      profileResult.duration
    ));

    // Test 2: Profile Update
    if (profileResult.ok) {
      const updateResult = await this.makeRequest('/api/profile', {
        method: 'PUT',
        body: JSON.stringify({
          bio: 'Updated bio for testing',
          socialMediaLinks: { linkedin: 'https://linkedin.com/in/testuser' }
        })
      });

      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'Profile Update',
        updateResult.ok ? 'PASS' : 'FAIL',
        updateResult.ok 
          ? 'Profile updated successfully'
          : `Profile update failed: ${updateResult.data?.error || 'Unknown error'}`,
        updateResult.duration
      ));
    }
  }

  // ==================== LEARNING PATH FLOWS ====================

  async testLearningPathFlow(): Promise<void> {
    console.log('📚 Testing Learning Path Flow...');
    
    // Test 1: Learning Resources Access
    const resourcesResult = await this.makeRequest('/api/learning-resources');
    
    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Learning Resources Access',
      resourcesResult.ok ? 'PASS' : 'FAIL',
      resourcesResult.ok 
        ? `Loaded ${resourcesResult.data?.length || 0} learning resources`
        : `Failed to load resources: ${resourcesResult.data?.error || 'Unknown error'}`,
      resourcesResult.duration
    ));

    // Test 2: Resource Rating
    if (resourcesResult.ok && resourcesResult.data?.length > 0) {
      const firstResource = resourcesResult.data[0];
      const ratingResult = await this.makeRequest(`/api/resource-ratings`, {
        method: 'POST',
        body: JSON.stringify({
          resourceId: firstResource.id,
          rating: 5,
          review: 'Excellent resource for testing!'
        })
      });

      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'Resource Rating',
        ratingResult.ok ? 'PASS' : 'FAIL',
        ratingResult.ok 
          ? 'Successfully rated resource'
          : `Rating failed: ${ratingResult.data?.error || 'Unknown error'}`,
        ratingResult.duration
      ));
    }

    // Test 3: Progress Tracking
    const progressResult = await this.makeRequest('/api/progress-tracker');
    
    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Progress Tracking',
      progressResult.ok ? 'PASS' : 'FAIL',
      progressResult.ok 
        ? 'Progress tracking functional'
        : `Progress tracking failed: ${progressResult.data?.error || 'Unknown error'}`,
      progressResult.duration
    ));

    // Test 4: Recommendations
    const recommendationsResult = await this.makeRequest('/api/recommendations');
    
    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Personalized Recommendations',
      recommendationsResult.ok ? 'PASS' : 'FAIL',
      recommendationsResult.ok 
        ? `Generated ${recommendationsResult.data?.recommendations?.length || 0} recommendations`
        : `Recommendations failed: ${recommendationsResult.data?.error || 'Unknown error'}`,
      recommendationsResult.duration
    ));
  }

  // ==================== ASSESSMENT FLOW ====================

  async testAssessmentFlow(): Promise<void> {
    console.log('🎯 Testing Assessment Flow...');
    
    // Test 1: Assessment Access
    const assessmentResult = await this.makeRequest('/api/assessment');
    
    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Assessment Access',
      assessmentResult.ok ? 'PASS' : 'FAIL',
      assessmentResult.ok 
        ? 'Assessment system accessible'
        : `Assessment access failed: ${assessmentResult.data?.error || 'Unknown error'}`,
      assessmentResult.duration
    ));

    // Test 2: Assessment Submission
    const submissionResult = await this.makeRequest('/api/assessment', {
      method: 'POST',
      body: JSON.stringify({
        step: 1,
        responses: {
          interests: ['technology', 'problem-solving'],
          skills: ['analytical-thinking', 'communication']
        }
      })
    });

    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Assessment Submission',
      submissionResult.ok ? 'PASS' : 'FAIL',
      submissionResult.ok 
        ? 'Assessment responses saved successfully'
        : `Assessment submission failed: ${submissionResult.data?.error || 'Unknown error'}`,
      submissionResult.duration
    ));

    // Test 3: Career Suggestions
    const suggestionsResult = await this.makeRequest('/api/career-suggestions');
    
    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Career Suggestions',
      suggestionsResult.ok ? 'PASS' : 'FAIL',
      suggestionsResult.ok 
        ? `Generated ${suggestionsResult.data?.suggestions?.length || 0} career suggestions`
        : `Career suggestions failed: ${suggestionsResult.data?.error || 'Unknown error'}`,
      suggestionsResult.duration
    ));
  }

  // ==================== COMMUNITY FORUM FLOWS ====================

  async testCommunityForumFlow(): Promise<void> {
    console.log('💬 Testing Community Forum Flow...');

    // Test 1: Forum Posts Access
    const postsResult = await this.makeRequest('/api/forum/posts');

    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Forum Posts Access',
      postsResult.ok ? 'PASS' : 'FAIL',
      postsResult.ok
        ? `Loaded ${postsResult.data?.length || 0} forum posts`
        : `Failed to load posts: ${postsResult.data?.error || 'Unknown error'}`,
      postsResult.duration
    ));

    // Test 2: Create Forum Post
    const createPostResult = await this.makeRequest('/api/forum/posts', {
      method: 'POST',
      body: JSON.stringify({
        title: 'Test Post for E2E Testing',
        content: 'This is a test post created during comprehensive testing.',
        category: 'GENERAL'
      })
    });

    this.addTestResult(this.createTestResult(
      'Core User Flows',
      'Forum Post Creation',
      createPostResult.ok ? 'PASS' : 'FAIL',
      createPostResult.ok
        ? 'Successfully created forum post'
        : `Post creation failed: ${createPostResult.data?.error || 'Unknown error'}`,
      createPostResult.duration
    ));

    // Test 3: Forum Post Interactions (if post was created)
    if (createPostResult.ok && createPostResult.data?.id) {
      const postId = createPostResult.data.id;

      // Test commenting
      const commentResult = await this.makeRequest(`/api/forum/posts/${postId}/replies`, {
        method: 'POST',
        body: JSON.stringify({
          content: 'This is a test comment for E2E testing.'
        })
      });

      this.addTestResult(this.createTestResult(
        'Core User Flows',
        'Forum Comment Creation',
        commentResult.ok ? 'PASS' : 'FAIL',
        commentResult.ok
          ? 'Successfully created forum comment'
          : `Comment creation failed: ${commentResult.data?.error || 'Unknown error'}`,
        commentResult.duration
      ));
    }
  }

  // ==================== TECHNICAL AREAS TESTING ====================

  async testBackendAPIEndpoints(): Promise<void> {
    console.log('🔧 Testing Backend API Endpoints...');

    const endpoints = [
      { path: '/api/auth/session', method: 'GET', requiresAuth: false },
      { path: '/api/auth/providers', method: 'GET', requiresAuth: false },
      { path: '/api/career-paths', method: 'GET', requiresAuth: false },
      { path: '/api/learning-resources', method: 'GET', requiresAuth: false },
      { path: '/api/learning-resources/categories', method: 'GET', requiresAuth: false },
      { path: '/api/profile', method: 'GET', requiresAuth: true },
      { path: '/api/assessment', method: 'GET', requiresAuth: true },
      { path: '/api/recommendations', method: 'GET', requiresAuth: true },
      { path: '/api/progress-tracker', method: 'GET', requiresAuth: true },
      { path: '/api/forum/posts', method: 'GET', requiresAuth: false },
    ];

    for (const endpoint of endpoints) {
      const result = await this.makeRequest(endpoint.path, { method: endpoint.method });

      this.addTestResult(this.createTestResult(
        'Technical Areas',
        `API Endpoint: ${endpoint.method} ${endpoint.path}`,
        result.ok ? 'PASS' : 'FAIL',
        result.ok
          ? `Response: ${result.status}, Duration: ${result.duration}ms`
          : `Failed: ${result.status} - ${result.data?.error || 'Unknown error'}`,
        result.duration
      ));
    }
  }

  async testDatabaseOperations(): Promise<void> {
    console.log('🗄️ Testing Database Operations...');

    try {
      // Test 1: Database Connection
      await prisma.$connect();
      this.addTestResult(this.createTestResult(
        'Technical Areas',
        'Database Connection',
        'PASS',
        'Successfully connected to database'
      ));

      // Test 2: User Query
      const userCount = await prisma.user.count();
      this.addTestResult(this.createTestResult(
        'Technical Areas',
        'User Data Query',
        'PASS',
        `Found ${userCount} users in database`
      ));

      // Test 3: Learning Resources Query
      const resourceCount = await prisma.learningResource.count();
      this.addTestResult(this.createTestResult(
        'Technical Areas',
        'Learning Resources Query',
        'PASS',
        `Found ${resourceCount} learning resources in database`
      ));

      // Test 4: Assessment Data Query
      const assessmentCount = await prisma.assessment.count();
      this.addTestResult(this.createTestResult(
        'Technical Areas',
        'Assessment Data Query',
        'PASS',
        `Found ${assessmentCount} assessments in database`
      ));

      // Test 5: Forum Posts Query
      const forumPostCount = await prisma.forumPost.count();
      this.addTestResult(this.createTestResult(
        'Technical Areas',
        'Forum Posts Query',
        'PASS',
        `Found ${forumPostCount} forum posts in database`
      ));

    } catch (error) {
      this.addTestResult(this.createTestResult(
        'Technical Areas',
        'Database Operations',
        'FAIL',
        `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      ));
    }
  }

  async testAuthenticationSecurity(): Promise<void> {
    console.log('🔒 Testing Authentication & Authorization...');

    // Test 1: Protected Route Access Without Auth
    const protectedResult = await this.makeRequest('/api/profile', {
      headers: { 'Cookie': '' } // No session cookie
    });

    this.addTestResult(this.createTestResult(
      'Technical Areas',
      'Protected Route Security',
      !protectedResult.ok ? 'PASS' : 'FAIL',
      !protectedResult.ok
        ? 'Correctly blocked unauthorized access'
        : 'Failed to block unauthorized access',
      protectedResult.duration
    ));

    // Test 2: Session Token Validation
    const sessionResult = await this.makeRequest('/api/auth/session');

    this.addTestResult(this.createTestResult(
      'Technical Areas',
      'Session Token Validation',
      sessionResult.ok ? 'PASS' : 'FAIL',
      sessionResult.ok
        ? 'Session validation working correctly'
        : 'Session validation failed',
      sessionResult.duration
    ));

    // Test 3: Password Security (check if passwords are hashed)
    try {
      const user = await prisma.user.findFirst({
        where: { email: { contains: '@' } }
      });

      if (user && user.password) {
        const isHashed = user.password.startsWith('$2') && user.password.length >= 60;
        this.addTestResult(this.createTestResult(
          'Technical Areas',
          'Password Security',
          isHashed ? 'PASS' : 'FAIL',
          isHashed
            ? 'Passwords are properly hashed'
            : 'Passwords may not be properly hashed'
        ));
      }
    } catch (error) {
      this.addTestResult(this.createTestResult(
        'Technical Areas',
        'Password Security',
        'FAIL',
        `Error checking password security: ${error instanceof Error ? error.message : 'Unknown error'}`
      ));
    }
  }

  // ==================== EDGE CASES & ERROR HANDLING ====================

  async testEdgeCasesAndErrorHandling(): Promise<void> {
    console.log('⚠️ Testing Edge Cases & Error Handling...');

    // Test 1: Invalid Input Validation
    const invalidEmailResult = await this.makeRequest('/api/signup', {
      method: 'POST',
      body: JSON.stringify({
        email: 'invalid-email',
        password: 'test123',
        name: 'Test User'
      })
    });

    this.addTestResult(this.createTestResult(
      'Edge Cases & Error Handling',
      'Invalid Email Validation',
      !invalidEmailResult.ok ? 'PASS' : 'FAIL',
      !invalidEmailResult.ok
        ? 'Correctly rejected invalid email format'
        : 'Failed to validate email format',
      invalidEmailResult.duration
    ));

    // Test 2: Weak Password Validation
    const weakPasswordResult = await this.makeRequest('/api/signup', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '123',
        name: 'Test User'
      })
    });

    this.addTestResult(this.createTestResult(
      'Edge Cases & Error Handling',
      'Weak Password Validation',
      !weakPasswordResult.ok ? 'PASS' : 'FAIL',
      !weakPasswordResult.ok
        ? 'Correctly rejected weak password'
        : 'Failed to validate password strength',
      weakPasswordResult.duration
    ));

    // Test 3: Malformed JSON Handling
    const malformedJsonResult = await this.makeRequest('/api/assessment', {
      method: 'POST',
      body: '{"invalid": json}'
    });

    this.addTestResult(this.createTestResult(
      'Edge Cases & Error Handling',
      'Malformed JSON Handling',
      !malformedJsonResult.ok ? 'PASS' : 'FAIL',
      !malformedJsonResult.ok
        ? 'Correctly handled malformed JSON'
        : 'Failed to handle malformed JSON',
      malformedJsonResult.duration
    ));

    // Test 4: Non-existent Resource Access
    const nonExistentResult = await this.makeRequest('/api/learning-resources/non-existent-id');

    this.addTestResult(this.createTestResult(
      'Edge Cases & Error Handling',
      'Non-existent Resource Handling',
      nonExistentResult.status === 404 ? 'PASS' : 'FAIL',
      nonExistentResult.status === 404
        ? 'Correctly returned 404 for non-existent resource'
        : `Unexpected response: ${nonExistentResult.status}`,
      nonExistentResult.duration
    ));

    // Test 5: Large Payload Handling
    const largePayload = 'x'.repeat(10000); // 10KB string
    const largePayloadResult = await this.makeRequest('/api/profile', {
      method: 'PUT',
      body: JSON.stringify({
        bio: largePayload
      })
    });

    this.addTestResult(this.createTestResult(
      'Edge Cases & Error Handling',
      'Large Payload Handling',
      largePayloadResult.status === 413 || !largePayloadResult.ok ? 'PASS' : 'FAIL',
      largePayloadResult.status === 413 || !largePayloadResult.ok
        ? 'Correctly handled large payload'
        : 'Failed to limit payload size',
      largePayloadResult.duration
    ));
  }

  // ==================== PERFORMANCE TESTING ====================

  async testPerformance(): Promise<void> {
    console.log('⚡ Testing Performance...');

    // Test 1: API Response Times
    const performanceTests = [
      { endpoint: '/api/learning-resources', threshold: 1000 },
      { endpoint: '/api/career-paths', threshold: 1000 },
      { endpoint: '/api/forum/posts', threshold: 1500 },
      { endpoint: '/api/recommendations', threshold: 2000 },
      { endpoint: '/api/progress-tracker', threshold: 1000 }
    ];

    for (const test of performanceTests) {
      const result = await this.makeRequest(test.endpoint);

      this.addTestResult(this.createTestResult(
        'Performance Testing',
        `Response Time: ${test.endpoint}`,
        result.duration && result.duration < test.threshold ? 'PASS' : 'FAIL',
        `Response time: ${result.duration}ms (threshold: ${test.threshold}ms)`,
        result.duration
      ));
    }

    // Test 2: Concurrent Request Handling
    const concurrentRequests = Array(5).fill(null).map(() =>
      this.makeRequest('/api/learning-resources')
    );

    const startTime = Date.now();
    const results = await Promise.all(concurrentRequests);
    const totalTime = Date.now() - startTime;

    const allSuccessful = results.every(r => r.ok);
    this.addTestResult(this.createTestResult(
      'Performance Testing',
      'Concurrent Request Handling',
      allSuccessful ? 'PASS' : 'FAIL',
      `Handled 5 concurrent requests in ${totalTime}ms`,
      totalTime
    ));

    // Test 3: Database Query Performance
    try {
      const startTime = Date.now();
      await prisma.learningResource.findMany({
        include: {
          ratings: true,
          careerPaths: true
        }
      });
      const queryTime = Date.now() - startTime;

      this.addTestResult(this.createTestResult(
        'Performance Testing',
        'Database Query Performance',
        queryTime < 500 ? 'PASS' : 'FAIL',
        `Complex query executed in ${queryTime}ms`,
        queryTime
      ));
    } catch (error) {
      this.addTestResult(this.createTestResult(
        'Performance Testing',
        'Database Query Performance',
        'FAIL',
        `Query failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      ));
    }
  }

  // ==================== SECURITY TESTING ====================

  async testSecurityVulnerabilities(): Promise<void> {
    console.log('🛡️ Testing Security Vulnerabilities...');

    // Test 1: SQL Injection Prevention
    const sqlInjectionResult = await this.makeRequest('/api/learning-resources', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        search: "'; DROP TABLE users; --"
      })
    });

    this.addTestResult(this.createTestResult(
      'Security Testing',
      'SQL Injection Prevention',
      sqlInjectionResult.ok || sqlInjectionResult.status === 400 ? 'PASS' : 'FAIL',
      sqlInjectionResult.ok || sqlInjectionResult.status === 400
        ? 'SQL injection attempt handled safely'
        : 'Potential SQL injection vulnerability',
      sqlInjectionResult.duration
    ));

    // Test 2: XSS Prevention
    const xssPayload = '<script>alert("XSS")</script>';
    const xssResult = await this.makeRequest('/api/forum/posts', {
      method: 'POST',
      body: JSON.stringify({
        title: xssPayload,
        content: xssPayload,
        category: 'GENERAL'
      })
    });

    this.addTestResult(this.createTestResult(
      'Security Testing',
      'XSS Prevention',
      xssResult.ok ? 'PASS' : 'FAIL',
      xssResult.ok
        ? 'XSS payload handled safely (should be sanitized on display)'
        : 'XSS payload rejected',
      xssResult.duration
    ));

    // Test 3: Rate Limiting (if implemented)
    const rateLimitTests = [];
    for (let i = 0; i < 10; i++) {
      rateLimitTests.push(this.makeRequest('/api/auth/session'));
    }

    const rateLimitResults = await Promise.all(rateLimitTests);
    const blockedRequests = rateLimitResults.filter(r => r.status === 429).length;

    this.addTestResult(this.createTestResult(
      'Security Testing',
      'Rate Limiting',
      blockedRequests > 0 ? 'PASS' : 'SKIP',
      blockedRequests > 0
        ? `Rate limiting active: ${blockedRequests}/10 requests blocked`
        : 'Rate limiting not detected (may not be implemented)',
    ));

    // Test 4: Authorization Boundary Testing
    const unauthorizedActions = [
      { endpoint: '/api/profile', method: 'PUT', description: 'Profile update without auth' },
      { endpoint: '/api/assessment', method: 'POST', description: 'Assessment submission without auth' },
      { endpoint: '/api/forum/posts', method: 'POST', description: 'Forum post creation without auth' }
    ];

    for (const action of unauthorizedActions) {
      const result = await this.makeRequest(action.endpoint, {
        method: action.method,
        headers: { 'Cookie': '' }, // No session
        body: JSON.stringify({ test: 'data' })
      });

      this.addTestResult(this.createTestResult(
        'Security Testing',
        `Authorization: ${action.description}`,
        !result.ok ? 'PASS' : 'FAIL',
        !result.ok
          ? 'Correctly blocked unauthorized action'
          : 'Failed to block unauthorized action',
        result.duration
      ));
    }
  }

  // ==================== MAIN TEST EXECUTION ====================

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Comprehensive End-to-End Testing...\n');

    try {
      // Core User Flows
      await this.testUserRegistrationFlow();
      await this.testAuthenticationFlow();
      await this.testProfileManagement();
      await this.testLearningPathFlow();
      await this.testAssessmentFlow();
      await this.testCommunityForumFlow();

      // Technical Areas
      await this.testBackendAPIEndpoints();
      await this.testDatabaseOperations();
      await this.testAuthenticationSecurity();

      // Edge Cases & Error Handling
      await this.testEdgeCasesAndErrorHandling();

      // Performance Testing
      await this.testPerformance();

      // Security Testing
      await this.testSecurityVulnerabilities();

      // Generate comprehensive report
      this.generateReport();

    } catch (error) {
      console.error('❌ Testing suite encountered an error:', error);
    } finally {
      await prisma.$disconnect();
    }
  }

  private generateReport(): void {
    console.log('\n📊 COMPREHENSIVE TEST REPORT');
    console.log('='.repeat(50));
    
    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalSkipped = 0;

    this.results.forEach(suite => {
      console.log(`\n📋 ${suite.name}`);
      console.log(`   Total: ${suite.summary.total} | Passed: ${suite.summary.passed} | Failed: ${suite.summary.failed} | Skipped: ${suite.summary.skipped}`);
      
      suite.tests.forEach(test => {
        const icon = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⏭️';
        console.log(`   ${icon} ${test.test}: ${test.details}`);
        if (test.duration) {
          console.log(`      Duration: ${test.duration}ms`);
        }
        if (test.error) {
          console.log(`      Error: ${test.error}`);
        }
      });

      totalTests += suite.summary.total;
      totalPassed += suite.summary.passed;
      totalFailed += suite.summary.failed;
      totalSkipped += suite.summary.skipped;
    });

    console.log('\n🎯 OVERALL SUMMARY');
    console.log('='.repeat(30));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${totalPassed} (${((totalPassed / totalTests) * 100).toFixed(1)}%)`);
    console.log(`Failed: ${totalFailed} (${((totalFailed / totalTests) * 100).toFixed(1)}%)`);
    console.log(`Skipped: ${totalSkipped} (${((totalSkipped / totalTests) * 100).toFixed(1)}%)`);
    
    const successRate = (totalPassed / totalTests) * 100;
    console.log(`\n🏆 Success Rate: ${successRate.toFixed(1)}%`);
    
    if (successRate >= 95) {
      console.log('🎉 EXCELLENT! System is highly stable and ready for production.');
    } else if (successRate >= 85) {
      console.log('✅ GOOD! System is stable with minor issues to address.');
    } else if (successRate >= 70) {
      console.log('⚠️  MODERATE! System needs attention before production.');
    } else {
      console.log('🚨 CRITICAL! System requires significant fixes before deployment.');
    }
  }
}

// Execute the comprehensive test suite
if (require.main === module) {
  const tester = new ComprehensiveE2ETester();
  tester.runAllTests().catch(console.error);
}

export default ComprehensiveE2ETester;
