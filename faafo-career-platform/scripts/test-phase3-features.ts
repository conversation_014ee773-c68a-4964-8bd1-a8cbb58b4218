#!/usr/bin/env tsx

/**
 * Test script for Phase 3: Career Path Enhancements
 * Tests bookmark functionality, action plan tracking, and enhanced recommendations
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface TestResult {
  test: string;
  passed: boolean;
  message: string;
  data?: any;
}

const results: TestResult[] = [];

async function runTest(testName: string, testFn: () => Promise<any>): Promise<void> {
  try {
    console.log(`\n🧪 Running: ${testName}`);
    const result = await testFn();
    results.push({
      test: testName,
      passed: true,
      message: 'Test passed successfully',
      data: result
    });
    console.log(`✅ ${testName} - PASSED`);
  } catch (error) {
    results.push({
      test: testName,
      passed: false,
      message: error instanceof Error ? error.message : 'Unknown error',
    });
    console.log(`❌ ${testName} - FAILED: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function testDatabaseSchema(): Promise<any> {
  // Test that new models exist and can be queried
  const bookmarkCount = await prisma.careerPathBookmark.count();
  const progressCount = await prisma.actionPlanProgress.count();
  
  return {
    bookmarkTableExists: true,
    progressTableExists: true,
    bookmarkCount,
    progressCount
  };
}

async function testCareerPathBookmarks(): Promise<any> {
  // Create a test user and career path for testing
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Phase 3 Test User'
    }
  });

  const testCareerPath = await prisma.careerPath.findFirst({
    where: { isActive: true }
  });

  if (!testCareerPath) {
    throw new Error('No active career paths found for testing');
  }

  // Test creating a bookmark
  const bookmark = await prisma.careerPathBookmark.create({
    data: {
      userId: testUser.id,
      careerPathId: testCareerPath.id,
      notes: 'Test bookmark for Phase 3'
    }
  });

  // Test retrieving bookmarks
  const userBookmarks = await prisma.careerPathBookmark.findMany({
    where: { userId: testUser.id },
    include: { careerPath: true }
  });

  // Test updating bookmark
  const updatedBookmark = await prisma.careerPathBookmark.update({
    where: { id: bookmark.id },
    data: { notes: 'Updated test bookmark' }
  });

  // Test deleting bookmark
  await prisma.careerPathBookmark.delete({
    where: { id: bookmark.id }
  });

  return {
    bookmarkCreated: !!bookmark,
    bookmarkRetrieved: userBookmarks.length > 0,
    bookmarkUpdated: updatedBookmark.notes === 'Updated test bookmark',
    bookmarkDeleted: true
  };
}

async function testActionPlanProgress(): Promise<any> {
  const testUser = await prisma.user.findFirst({
    where: { email: '<EMAIL>' }
  });

  const testCareerPath = await prisma.careerPath.findFirst({
    where: { isActive: true }
  });

  if (!testUser || !testCareerPath) {
    throw new Error('Test user or career path not found');
  }

  // Test creating progress entry
  const progress = await prisma.actionPlanProgress.create({
    data: {
      userId: testUser.id,
      careerPathId: testCareerPath.id,
      stepIndex: 0,
      stepTitle: 'Test Step 1',
      isCompleted: false
    }
  });

  // Test updating progress
  const updatedProgress = await prisma.actionPlanProgress.update({
    where: { id: progress.id },
    data: { 
      isCompleted: true,
      completedAt: new Date(),
      notes: 'Completed test step'
    }
  });

  // Test retrieving progress for a career path
  const careerPathProgress = await prisma.actionPlanProgress.findMany({
    where: {
      userId: testUser.id,
      careerPathId: testCareerPath.id
    },
    orderBy: { stepIndex: 'asc' }
  });

  // Clean up
  await prisma.actionPlanProgress.delete({
    where: { id: progress.id }
  });

  return {
    progressCreated: !!progress,
    progressUpdated: updatedProgress.isCompleted === true,
    progressRetrieved: careerPathProgress.length > 0,
    progressDeleted: true
  };
}

async function testCareerPathAPI(): Promise<any> {
  // Test that career path API returns user-specific data
  const testCareerPath = await prisma.careerPath.findFirst({
    where: { isActive: true }
  });

  if (!testCareerPath) {
    throw new Error('No active career paths found');
  }

  // Test enhanced career path data structure
  const enhancedCareerPath = await prisma.careerPath.findUnique({
    where: { id: testCareerPath.id },
    include: {
      relatedSkills: true,
      relatedIndustries: true,
      learningResources: {
        where: { isActive: true },
        take: 3
      },
      bookmarks: true,
      actionPlanProgress: true
    }
  });

  return {
    careerPathFound: !!enhancedCareerPath,
    hasRelatedSkills: enhancedCareerPath?.relatedSkills.length || 0,
    hasLearningResources: enhancedCareerPath?.learningResources.length || 0,
    bookmarksRelationExists: Array.isArray(enhancedCareerPath?.bookmarks),
    progressRelationExists: Array.isArray(enhancedCareerPath?.actionPlanProgress)
  };
}

async function testRecommendationData(): Promise<any> {
  // Test data needed for enhanced recommendations
  const careerPathsWithRelations = await prisma.careerPath.findMany({
    where: { isActive: true },
    include: {
      relatedSkills: true,
      relatedIndustries: true,
      bookmarks: true,
      suggestionRules: true
    },
    take: 5
  });

  const assessmentsWithResponses = await prisma.assessment.findMany({
    where: { status: 'COMPLETED' },
    include: { responses: true },
    take: 3
  });

  return {
    careerPathsFound: careerPathsWithRelations.length,
    careerPathsHaveSkills: careerPathsWithRelations.filter(cp => cp.relatedSkills.length > 0).length,
    careerPathsHaveBookmarks: careerPathsWithRelations.filter(cp => cp.bookmarks.length > 0).length,
    assessmentsFound: assessmentsWithResponses.length,
    assessmentsHaveResponses: assessmentsWithResponses.filter(a => a.responses.length > 0).length
  };
}

async function testDataIntegrity(): Promise<any> {
  // Test that we can query the tables without errors
  const totalBookmarks = await prisma.careerPathBookmark.count();
  const totalProgress = await prisma.actionPlanProgress.count();

  // Test that relationships work
  const bookmarksWithUsers = await prisma.careerPathBookmark.findMany({
    include: { user: true, careerPath: true },
    take: 5
  });

  const progressWithRelations = await prisma.actionPlanProgress.findMany({
    include: { user: true, careerPath: true },
    take: 5
  });

  return {
    totalBookmarks,
    totalProgress,
    bookmarksWithRelations: bookmarksWithUsers.length,
    progressWithRelations: progressWithRelations.length,
    relationshipsWork: true
  };
}

async function main() {
  console.log('🚀 Starting Phase 3: Career Path Enhancements Tests\n');
  console.log('=' .repeat(60));

  await runTest('Database Schema Validation', testDatabaseSchema);
  await runTest('Career Path Bookmarks CRUD', testCareerPathBookmarks);
  await runTest('Action Plan Progress Tracking', testActionPlanProgress);
  await runTest('Enhanced Career Path API', testCareerPathAPI);
  await runTest('Recommendation System Data', testRecommendationData);
  await runTest('Data Integrity Checks', testDataIntegrity);

  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));

  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => r.passed === false).length;
  const total = results.length;

  console.log(`\nTotal Tests: ${total}`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / total) * 100)}%`);

  if (failed > 0) {
    console.log('\n🔍 FAILED TESTS:');
    results.filter(r => !r.passed).forEach(result => {
      console.log(`  ❌ ${result.test}: ${result.message}`);
    });
  }

  if (passed === total) {
    console.log('\n🎉 All Phase 3 features are working correctly!');
    console.log('\n✨ Phase 3 Implementation Summary:');
    console.log('  📌 Career Path Bookmarking - ✅ Implemented');
    console.log('  📋 Action Plan Progress Tracking - ✅ Implemented');
    console.log('  🎯 Enhanced Recommendations - ✅ Implemented');
    console.log('  🔗 Database Relations - ✅ Implemented');
    console.log('  🛡️  Data Integrity - ✅ Verified');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
    process.exit(1);
  }

  await prisma.$disconnect();
}

main().catch((error) => {
  console.error('💥 Test execution failed:', error);
  process.exit(1);
});
