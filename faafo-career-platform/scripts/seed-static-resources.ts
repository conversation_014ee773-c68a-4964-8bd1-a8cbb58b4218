import { PrismaClient, LearningResourceType, LearningResourceCategory, SkillLevel, LearningResourceCost, LearningResourceFormat } from '@prisma/client';

const prisma = new PrismaClient();

// Static resources from the resources page
const mindsetResources = [
  {
    title: 'Overcoming Fear of Career Change',
    description: 'A comprehensive guide to understanding and conquering the fears that hold you back from making a career transition.',
    url: 'https://www.themuse.com/advice/how-to-overcome-fear-of-career-change',
    type: 'ARTICLE' as const,
    category: 'ENTREPRENEURSHIP' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'The Muse',
    duration: '8 min read',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'Financial Planning for Career Transitions',
    description: 'Learn how to build a financial safety net and plan your finances before making a major career change.',
    url: 'https://www.nerdwallet.com/article/finance/financial-planning-career-change',
    type: 'ARTICLE' as const,
    category: 'FINANCIAL_LITERACY' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'NerdWallet',
    duration: '10 min read',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'The Psychology of Career Change',
    description: 'Understanding the emotional journey of career transitions and how to navigate psychological challenges.',
    url: 'https://hbr.org/2019/09/the-psychology-of-career-change',
    type: 'ARTICLE' as const,
    category: 'ENTREPRENEURSHIP' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'Harvard Business Review',
    duration: '12 min read',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'Building Confidence During Career Transitions',
    description: 'Practical strategies to build self-confidence and overcome self-doubt when changing careers.',
    url: 'https://www.forbes.com/sites/ashiraprossack1/2019/07/15/how-to-build-confidence-during-a-career-change/',
    type: 'ARTICLE' as const,
    category: 'ENTREPRENEURSHIP' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'Forbes',
    duration: '7 min read',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'The Complete Guide to Career Pivoting',
    description: 'A comprehensive resource for planning and executing a successful career pivot at any stage of life.',
    url: 'https://www.linkedin.com/pulse/complete-guide-career-pivoting-jenny-foss/',
    type: 'ARTICLE' as const,
    category: 'ENTREPRENEURSHIP' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'LinkedIn Learning',
    duration: '15 min read',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
];

const learningResources = [
  // Cybersecurity
  {
    title: 'Ethical Hacking Essentials (E|HE)',
    description: 'Strong foundations in ethical hacking and penetration testing for entry-level careers',
    url: 'https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/',
    type: 'COURSE' as const,
    category: 'CYBERSECURITY' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'Network Defense Essentials (N|DE)',
    description: 'Fundamentals of network security, protocols, controls, and identity/access management',
    url: 'https://www.eccouncil.org/train-certify/network-defense-essentials-nde/',
    type: 'COURSE' as const,
    category: 'CYBERSECURITY' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'CISA Cybersecurity Training',
    description: 'Government-backed cybersecurity training from beginner to advanced levels',
    url: 'https://www.cisa.gov/cybersecurity-training-exercises',
    type: 'COURSE' as const,
    category: 'CYBERSECURITY' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'CISA',
    duration: 'Various modules',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  // Data Science
  {
    title: 'Data Science: Machine Learning',
    description: 'Basics of machine learning, cross-validation, popular algorithms, and avoiding overtraining',
    url: 'https://pll.harvard.edu/course/data-science-machine-learning',
    type: 'COURSE' as const,
    category: 'DATA_SCIENCE' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'Harvard University',
    duration: 'Self-paced',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'Machine Learning Crash Course',
    description: 'Fast-paced introduction with animated videos, interactive visualizations, and hands-on practice',
    url: 'https://developers.google.com/machine-learning/crash-course',
    type: 'COURSE' as const,
    category: 'DATA_SCIENCE' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'Google',
    duration: 'Self-paced',
    cost: 'FREE' as const,
    format: 'INTERACTIVE' as const,
  },
  // AI
  {
    title: 'AI For Everyone',
    description: 'Non-technical introduction to AI concepts and applications',
    url: 'https://www.coursera.org/learn/ai-for-everyone',
    type: 'COURSE' as const,
    category: 'ARTIFICIAL_INTELLIGENCE' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'DeepLearning.AI',
    duration: 'Self-paced',
    cost: 'FREEMIUM' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'Elements of AI',
    description: 'Introduction to AI concepts and their practical applications',
    url: 'https://www.elementsofai.com/',
    type: 'COURSE' as const,
    category: 'ARTIFICIAL_INTELLIGENCE' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'University of Helsinki',
    duration: 'Self-paced',
    cost: 'FREE' as const,
    format: 'INTERACTIVE' as const,
  },
  // Digital Marketing
  {
    title: 'Fundamentals of Digital Marketing',
    description: 'Comprehensive introduction to digital marketing concepts and tools',
    url: 'https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing',
    type: 'COURSE' as const,
    category: 'DIGITAL_MARKETING' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'Google Digital Garage',
    duration: 'Self-paced',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  {
    title: 'Social Media Marketing',
    description: 'Introduction to social media marketing strategies and platforms',
    url: 'https://academy.hubspot.com/courses/social-media',
    type: 'COURSE' as const,
    category: 'DIGITAL_MARKETING' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'HubSpot Academy',
    duration: 'Self-paced',
    cost: 'FREE' as const,
    format: 'SELF_PACED' as const,
  },
  // Blockchain
  {
    title: 'Blockchain Basics',
    description: 'Introduction to blockchain technology, cryptocurrency, and smart contracts',
    url: 'https://www.coursera.org/learn/blockchain-basics',
    type: 'COURSE' as const,
    category: 'BLOCKCHAIN' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'University at Buffalo',
    duration: 'Self-paced',
    cost: 'FREEMIUM' as const,
    format: 'SELF_PACED' as const,
  },
  // Project Management
  {
    title: 'Project Management Foundations',
    description: 'Introduction to project management principles and methodologies',
    url: 'https://www.linkedin.com/learning/project-management-foundations-2019',
    type: 'COURSE' as const,
    category: 'PROJECT_MANAGEMENT' as const,
    skillLevel: 'BEGINNER' as const,
    author: 'LinkedIn Learning',
    duration: 'Video course',
    cost: 'FREEMIUM' as const,
    format: 'INSTRUCTOR_LED' as const,
  },
];

async function seedStaticResources() {
  console.log('🌱 Starting to seed static resources...');

  try {
    // Combine all resources
    const allResources = [...mindsetResources, ...learningResources];

    console.log(`📚 Seeding ${allResources.length} resources...`);

    for (const resource of allResources) {
      try {
        // Check if resource already exists
        const existing = await prisma.learningResource.findUnique({
          where: { url: resource.url },
        });

        if (existing) {
          console.log(`⏭️  Skipping existing resource: ${resource.title}`);
          continue;
        }

        // Create the resource
        const created = await prisma.learningResource.create({
          data: resource,
        });

        console.log(`✅ Created resource: ${created.title}`);
      } catch (error) {
        console.error(`❌ Error creating resource "${resource.title}":`, error);
      }
    }

    console.log('🎉 Static resources seeding completed!');

    // Print summary
    const totalResources = await prisma.learningResource.count();
    console.log(`📊 Total resources in database: ${totalResources}`);

    const resourcesByCategory = await prisma.learningResource.groupBy({
      by: ['category'],
      _count: {
        category: true,
      },
    });

    console.log('\n📈 Resources by category:');
    resourcesByCategory.forEach(({ category, _count }) => {
      console.log(`  ${category}: ${_count.category}`);
    });

  } catch (error) {
    console.error('❌ Error seeding static resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedStaticResources();
}

export default seedStaticResources;
