import * as dotenv from 'dotenv';
dotenv.config({ path: '/Users/<USER>/faafo/faafo-career-platform/.env' });

import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

async function sendTestEmail() {
  try {
    const { data, error } = await resend.emails.send({
      from: '<EMAIL>', // Resend provides a default domain for testing
      to: '<EMAIL>',
      subject: 'Test Email from FAAFO Career Platform',
      html: '<strong>This is a test email from your FAAFO Career Platform Resend integration!</strong>',
    });

    if (error) {
      console.error('Error sending test email:', error);
      return;
    }

    console.log('Test email sent successfully:', data);
  } catch (error) {
    console.error('Caught an exception while sending test email:', error);
  }
}

sendTestEmail(); 