import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedForumCategories() {
  console.log('🌱 Seeding forum categories...');

  try {
    // Create main categories
    const careerAdviceCategory = await prisma.forumCategory.upsert({
      where: { slug: 'career-advice' },
      update: {},
      create: {
        name: 'Career Advice',
        slug: 'career-advice',
        description: 'Get guidance and advice on career transitions, job searching, and professional development.',
        guidelines: 'Please be respectful and constructive in your advice. Share personal experiences and actionable tips.',
        icon: '💼',
        color: '#3B82F6',
        sortOrder: 1,
      },
    });

    const industryDiscussionsCategory = await prisma.forumCategory.upsert({
      where: { slug: 'industry-discussions' },
      update: {},
      create: {
        name: 'Industry Discussions',
        slug: 'industry-discussions',
        description: 'Discuss trends, opportunities, and insights across different industries.',
        guidelines: 'Share industry knowledge, trends, and professional insights. Keep discussions professional and informative.',
        icon: '🏢',
        color: '#10B981',
        sortOrder: 2,
      },
    });

    const learningResourcesCategory = await prisma.forumCategory.upsert({
      where: { slug: 'learning-resources' },
      update: {},
      create: {
        name: 'Learning Resources',
        slug: 'learning-resources',
        description: 'Share and discover educational content, courses, books, and learning materials.',
        guidelines: 'Share high-quality learning resources and provide honest reviews. Include details about cost, difficulty level, and time commitment.',
        icon: '📚',
        color: '#8B5CF6',
        sortOrder: 3,
      },
    });

    const jobOpportunitiesCategory = await prisma.forumCategory.upsert({
      where: { slug: 'job-opportunities' },
      update: {},
      create: {
        name: 'Job Opportunities',
        slug: 'job-opportunities',
        description: 'Share job openings, networking opportunities, and career events.',
        guidelines: 'Post legitimate job opportunities and networking events. Include relevant details like location, requirements, and application process.',
        icon: '🎯',
        color: '#F59E0B',
        sortOrder: 4,
      },
    });

    const successStoriesCategory = await prisma.forumCategory.upsert({
      where: { slug: 'success-stories' },
      update: {},
      create: {
        name: 'Success Stories',
        slug: 'success-stories',
        description: 'Share your career transition successes and inspire others.',
        guidelines: 'Share your journey, challenges overcome, and lessons learned. Be specific about strategies that worked.',
        icon: '🌟',
        color: '#EF4444',
        sortOrder: 5,
      },
    });

    // Create subcategories for Career Advice
    await prisma.forumCategory.upsert({
      where: { slug: 'resume-cv-help' },
      update: {},
      create: {
        name: 'Resume & CV Help',
        slug: 'resume-cv-help',
        description: 'Get feedback on your resume and CV.',
        parentId: careerAdviceCategory.id,
        icon: '📄',
        sortOrder: 1,
      },
    });

    await prisma.forumCategory.upsert({
      where: { slug: 'interview-preparation' },
      update: {},
      create: {
        name: 'Interview Preparation',
        slug: 'interview-preparation',
        description: 'Tips and practice for job interviews.',
        parentId: careerAdviceCategory.id,
        icon: '🎤',
        sortOrder: 2,
      },
    });

    await prisma.forumCategory.upsert({
      where: { slug: 'salary-negotiation' },
      update: {},
      create: {
        name: 'Salary Negotiation',
        slug: 'salary-negotiation',
        description: 'Strategies for negotiating compensation.',
        parentId: careerAdviceCategory.id,
        icon: '💰',
        sortOrder: 3,
      },
    });

    // Create subcategories for Industry Discussions
    await prisma.forumCategory.upsert({
      where: { slug: 'technology' },
      update: {},
      create: {
        name: 'Technology',
        slug: 'technology',
        description: 'Discuss tech industry trends and opportunities.',
        parentId: industryDiscussionsCategory.id,
        icon: '💻',
        sortOrder: 1,
      },
    });

    await prisma.forumCategory.upsert({
      where: { slug: 'healthcare' },
      update: {},
      create: {
        name: 'Healthcare',
        slug: 'healthcare',
        description: 'Healthcare industry discussions and career paths.',
        parentId: industryDiscussionsCategory.id,
        icon: '🏥',
        sortOrder: 2,
      },
    });

    await prisma.forumCategory.upsert({
      where: { slug: 'finance' },
      update: {},
      create: {
        name: 'Finance',
        slug: 'finance',
        description: 'Financial services and fintech career discussions.',
        parentId: industryDiscussionsCategory.id,
        icon: '📈',
        sortOrder: 3,
      },
    });

    await prisma.forumCategory.upsert({
      where: { slug: 'education' },
      update: {},
      create: {
        name: 'Education',
        slug: 'education',
        description: 'Teaching, training, and educational career paths.',
        parentId: industryDiscussionsCategory.id,
        icon: '🎓',
        sortOrder: 4,
      },
    });

    // Create subcategories for Learning Resources
    await prisma.forumCategory.upsert({
      where: { slug: 'online-courses' },
      update: {},
      create: {
        name: 'Online Courses',
        slug: 'online-courses',
        description: 'Reviews and recommendations for online courses.',
        parentId: learningResourcesCategory.id,
        icon: '🖥️',
        sortOrder: 1,
      },
    });

    await prisma.forumCategory.upsert({
      where: { slug: 'books-publications' },
      update: {},
      create: {
        name: 'Books & Publications',
        slug: 'books-publications',
        description: 'Recommended reading for career development.',
        parentId: learningResourcesCategory.id,
        icon: '📖',
        sortOrder: 2,
      },
    });

    await prisma.forumCategory.upsert({
      where: { slug: 'certifications' },
      update: {},
      create: {
        name: 'Certifications',
        slug: 'certifications',
        description: 'Professional certifications and their value.',
        parentId: learningResourcesCategory.id,
        icon: '🏆',
        sortOrder: 3,
      },
    });

    console.log('✅ Forum categories seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding forum categories:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedForumCategories()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export default seedForumCategories;
