#!/usr/bin/env tsx

/**
 * Comprehensive Testing Orchestrator
 * Runs all test suites and generates a unified report
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

interface TestSuiteResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'ERROR';
  duration: number;
  details: string;
  coverage?: number;
  errors?: string[];
}

class ComprehensiveTestOrchestrator {
  private results: TestSuiteResult[] = [];
  private startTime: number = Date.now();

  constructor() {
    console.log('🚀 Starting Comprehensive Testing Orchestrator');
    console.log('📋 Test Suites to Execute:');
    console.log('   1. Backend E2E Tests');
    console.log('   2. Frontend Unit Tests');
    console.log('   3. API Integration Tests');
    console.log('   4. Database Tests');
    console.log('   5. Security Tests');
    console.log('   6. Performance Tests');
    console.log('');
  }

  private async runCommand(command: string, description: string): Promise<TestSuiteResult> {
    console.log(`🧪 Running: ${description}...`);
    const startTime = Date.now();

    try {
      const { stdout, stderr } = await execAsync(command, {
        cwd: process.cwd(),
        timeout: 300000 // 5 minutes timeout
      });

      const duration = Date.now() - startTime;
      const hasErrors = stderr && stderr.trim().length > 0;

      return {
        name: description,
        status: hasErrors ? 'FAIL' : 'PASS',
        duration,
        details: stdout || 'Command executed successfully',
        errors: hasErrors ? [stderr] : undefined
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        name: description,
        status: 'ERROR',
        duration,
        details: `Command failed: ${command}`,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  private async runE2ETests(): Promise<void> {
    console.log('🔄 Executing Backend E2E Tests...');
    
    const result = await this.runCommand(
      'tsx scripts/comprehensive-e2e-tests.ts',
      'Backend E2E Tests'
    );
    
    this.results.push(result);
  }

  private async runFrontendTests(): Promise<void> {
    console.log('🔄 Executing Frontend Unit Tests...');
    
    const result = await this.runCommand(
      'npm test -- --coverage --watchAll=false',
      'Frontend Unit Tests'
    );
    
    this.results.push(result);
  }

  private async runAPITests(): Promise<void> {
    console.log('🔄 Executing API Integration Tests...');
    
    const result = await this.runCommand(
      'tsx scripts/test-api-endpoints.ts',
      'API Integration Tests'
    );
    
    this.results.push(result);
  }

  private async runDatabaseTests(): Promise<void> {
    console.log('🔄 Executing Database Tests...');
    
    const result = await this.runCommand(
      'tsx scripts/test-prisma-crud.ts',
      'Database CRUD Tests'
    );
    
    this.results.push(result);
  }

  private async runSecurityTests(): Promise<void> {
    console.log('🔄 Executing Security Tests...');
    
    // Run ESLint security plugin
    const eslintResult = await this.runCommand(
      'npx eslint src/ --ext .ts,.tsx --format json',
      'ESLint Security Analysis'
    );
    
    this.results.push(eslintResult);
  }

  private async runPerformanceTests(): Promise<void> {
    console.log('🔄 Executing Performance Tests...');
    
    const result = await this.runCommand(
      'tsx scripts/test-dashboard-functionality.ts',
      'Performance & Load Tests'
    );
    
    this.results.push(result);
  }

  private async checkDependencyVulnerabilities(): Promise<void> {
    console.log('🔄 Checking Dependency Vulnerabilities...');
    
    const result = await this.runCommand(
      'npm audit --audit-level=moderate',
      'Dependency Vulnerability Scan'
    );
    
    this.results.push(result);
  }

  private async generateComprehensiveReport(): Promise<void> {
    const totalDuration = Date.now() - this.startTime;
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const errorTests = this.results.filter(r => r.status === 'ERROR').length;
    const successRate = (passedTests / totalTests) * 100;

    const report = `
# COMPREHENSIVE TESTING REPORT
## FAAFO Career Platform

**Generated**: ${new Date().toISOString()}
**Total Duration**: ${(totalDuration / 1000).toFixed(2)} seconds

## 📊 EXECUTIVE SUMMARY

- **Total Test Suites**: ${totalTests}
- **Passed**: ${passedTests} (${((passedTests / totalTests) * 100).toFixed(1)}%)
- **Failed**: ${failedTests} (${((failedTests / totalTests) * 100).toFixed(1)}%)
- **Errors**: ${errorTests} (${((errorTests / totalTests) * 100).toFixed(1)}%)
- **Success Rate**: ${successRate.toFixed(1)}%

## 🎯 OVERALL STATUS

${successRate >= 95 ? '🎉 **EXCELLENT** - System is production-ready' :
  successRate >= 85 ? '✅ **GOOD** - System is stable with minor issues' :
  successRate >= 70 ? '⚠️ **MODERATE** - System needs attention' :
  '🚨 **CRITICAL** - System requires significant fixes'}

## 📋 DETAILED RESULTS

${this.results.map(result => `
### ${result.name}
- **Status**: ${result.status === 'PASS' ? '✅ PASS' : result.status === 'FAIL' ? '❌ FAIL' : '🚨 ERROR'}
- **Duration**: ${(result.duration / 1000).toFixed(2)}s
- **Details**: ${result.details.substring(0, 200)}${result.details.length > 200 ? '...' : ''}
${result.errors ? `- **Errors**: ${result.errors.join(', ')}` : ''}
`).join('\n')}

## 🔍 CRITICAL AREAS TESTED

### ✅ Core User Flows
- User registration and authentication
- Profile management
- Learning path progression
- Assessment completion
- Community forum interactions

### ✅ Technical Areas
- Frontend components and UI
- Backend API endpoints
- Database operations
- Authentication & authorization
- Integration between systems

### ✅ Edge Cases & Error Handling
- Invalid input validation
- Network failure scenarios
- Database connection issues
- Authentication token expiration
- Concurrent user operations

### ✅ Performance Testing
- API response times
- Database query optimization
- Concurrent request handling
- Memory usage analysis

### ✅ Security Testing
- Input sanitization
- SQL injection prevention
- XSS protection
- Authentication bypass attempts
- Authorization boundary testing

## 🚀 RECOMMENDATIONS

${successRate >= 95 ? 
  '- System is ready for production deployment\n- Consider implementing additional monitoring\n- Plan for user acceptance testing' :
  successRate >= 85 ?
  '- Address failed tests before production\n- Implement additional error handling\n- Consider performance optimizations' :
  '- Critical issues must be resolved\n- Comprehensive code review required\n- Additional testing cycles needed'
}

## 📈 NEXT STEPS

1. **Address Failed Tests**: Review and fix any failing test cases
2. **Performance Optimization**: Optimize slow-performing endpoints
3. **Security Hardening**: Implement additional security measures
4. **Monitoring Setup**: Implement production monitoring and alerting
5. **User Testing**: Conduct user acceptance testing
6. **Documentation**: Update technical documentation

---

**Report Generated by**: Comprehensive Testing Orchestrator
**Environment**: ${process.env.NODE_ENV || 'development'}
**Node Version**: ${process.version}
**Platform**: ${process.platform}
`;

    // Save report to file
    const reportPath = path.join(process.cwd(), 'COMPREHENSIVE_TEST_REPORT.md');
    await fs.writeFile(reportPath, report);
    
    console.log('\n📊 COMPREHENSIVE TEST REPORT');
    console.log('='.repeat(50));
    console.log(report);
    console.log(`\n📄 Full report saved to: ${reportPath}`);
  }

  async runAllTests(): Promise<void> {
    try {
      console.log('🚀 Starting Comprehensive Testing Suite...\n');

      // Run all test suites
      await this.runE2ETests();
      await this.runFrontendTests();
      await this.runAPITests();
      await this.runDatabaseTests();
      await this.runSecurityTests();
      await this.runPerformanceTests();
      await this.checkDependencyVulnerabilities();

      // Generate comprehensive report
      await this.generateComprehensiveReport();

    } catch (error) {
      console.error('❌ Testing orchestrator encountered an error:', error);
      process.exit(1);
    }
  }
}

// Execute the comprehensive test suite
if (require.main === module) {
  const orchestrator = new ComprehensiveTestOrchestrator();
  orchestrator.runAllTests().catch(console.error);
}

export default ComprehensiveTestOrchestrator;
