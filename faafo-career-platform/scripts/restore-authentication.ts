// Script to restore authentication after testing
import fs from 'fs';
import path from 'path';

interface FileRestore {
  file: string;
  changes: Array<{
    search: string;
    replace: string;
  }>;
}

const filesToRestore: FileRestore[] = [
  {
    file: 'src/app/api/personalized-resources/route.ts',
    changes: [
      {
        search: `    // Temporarily bypass authentication for testing
    // TODO: Re-enable authentication after fixing the cookies issue
    const testUserId = 'cmbme6uay0000sbkt36tn57zd'; // Test user ID
    
    console.log('Using test user ID for personalized resources');
    
    // Get the session with proper request context
    // const session = await getServerSession(authOptions);
    
    // console.log('Session in personalized-resources:', session);
    
    // if (!session || !session.user?.id) {
    //   console.log('No session or user ID found');
    //   return NextResponse.json(
    //     { success: false, error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }`,
        replace: `    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }`
      },
      {
        search: `        userId: testUserId, // Use test user ID`,
        replace: `        userId: session.user.id,`
      }
    ]
  },
  {
    file: 'src/app/api/assessment/route.ts',
    changes: [
      {
        search: `  // Temporarily bypass authentication for testing
  const testUserId = 'cmbme6uay0000sbkt36tn57zd';
  
  // const session = await getServerSession(authOptions);

  // if (!session || !session.user || !session.user.id) {
  //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  // }`,
        replace: `  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }`
      },
      {
        search: `        userId: testUserId, // Use test user ID`,
        replace: `        userId: session.user.id,`
      }
    ]
  },
  {
    file: 'src/app/api/freedom-fund/route.ts',
    changes: [
      {
        search: `  // Temporarily bypass authentication for testing
  const testUserId = 'cmbme6uay0000sbkt36tn57zd';
  
  // const session = await getServerSession(authOptions);

  // if (!session || !session.user || !session.user.id) {
  //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  // }`,
        replace: `  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }`
      },
      {
        search: `      where: { userId: testUserId },`,
        replace: `      where: { userId: session.user.id },`
      }
    ]
  },
  {
    file: 'src/app/api/learning-progress/route.ts',
    changes: [
      {
        search: `    // Temporarily bypass authentication for testing
    const testUserId = 'cmbme6uay0000sbkt36tn57zd';
    
    // const session = await getServerSession(authOptions);
    
    // if (!session || !session.user?.id) {
    //   return NextResponse.json(
    //     { success: false, error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }`,
        replace: `    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }`
      },
      {
        search: `            userId: testUserId,`,
        replace: `            userId: session.user.id,`
      },
      {
        search: `          userId: testUserId`,
        replace: `          userId: session.user.id`
      },
      {
        search: `    // Temporarily bypass authentication for testing
    const testUserId = 'cmbme6uay0000sbkt36tn57zd';
    
    // const session = await getServerSession(authOptions);
    
    // if (!session || !session.user?.id) {
    //   return NextResponse.json(
    //     { success: false, error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }`,
        replace: `    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }`
      }
    ]
  },
  {
    file: 'src/app/dashboard/page.tsx',
    changes: [
      {
        search: `    // Temporarily bypass authentication for testing
    fetchDashboardData();
    
    // if (status === 'unauthenticated') {
    //   router.push('/login');
    //   return;
    // }

    // if (status === 'authenticated') {
    //   fetchDashboardData();
    // }`,
        replace: `    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchDashboardData();
    }`
      },
      {
        search: `  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading dashboard...</div>
      </div>
    );
  }

  // Temporarily bypass authentication check for testing
  // if (status === 'unauthenticated') {
  //   return (
  //     <div className="container mx-auto px-4 py-8">
  //       <div className="text-center">
  //         <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to access your dashboard.</p>
  //       </div>
  //     </div>
  //   );
  // }`,
        replace: `  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading dashboard...</div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to access your dashboard.</p>
        </div>
      </div>
    );
  }`
      },
      {
        search: `          Welcome back, Test User!`,
        replace: `          Welcome back, {session?.user?.name || session?.user?.email?.split('@')[0]}!`
      }
    ]
  },
  {
    file: 'src/components/dashboard/PersonalizedResources.tsx',
    changes: [
      {
        search: `    // Temporarily bypass session check for testing
    fetchPersonalizedResources();
    
    // if (session?.user?.id) {
    //   fetchPersonalizedResources();
    // }`,
        replace: `    if (session?.user?.id) {
      fetchPersonalizedResources();
    }`
      },
      {
        search: `  // Temporarily bypass session check for testing
  // if (!session) {
  //   return (
  //     <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
  //       <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
  //         <TrendingUp className="h-5 w-5 text-blue-600" />
  //         Personalized Learning Resources
  //       </h2>
  //       <div className="text-center py-8">
  //         <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
  //         <p className="text-gray-600 dark:text-gray-400 mb-4">
  //           Sign in to get personalized learning recommendations based on your assessment results.
  //         </p>
  //         <Button asChild>
  //           <Link href="/login">Sign In</Link>
  //         </Button>
  //       </div>
  //     </div>
  //   );
  // }`,
        replace: `  if (!session) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          Personalized Learning Resources
        </h2>
        <div className="text-center py-8">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Sign in to get personalized learning recommendations based on your assessment results.
          </p>
          <Button asChild>
            <Link href="/login">Sign In</Link>
          </Button>
        </div>
      </div>
    );
  }`
      }
    ]
  }
];

async function restoreAuthentication() {
  console.log('🔧 Restoring authentication in all files...\n');

  for (const fileRestore of filesToRestore) {
    const filePath = path.join(process.cwd(), fileRestore.file);
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let changesMade = 0;

      for (const change of fileRestore.changes) {
        if (content.includes(change.search)) {
          content = content.replace(change.search, change.replace);
          changesMade++;
        }
      }

      if (changesMade > 0) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ ${fileRestore.file}: ${changesMade} changes restored`);
      } else {
        console.log(`ℹ️  ${fileRestore.file}: No changes needed`);
      }

    } catch (error) {
      console.error(`❌ Error processing ${fileRestore.file}:`, error);
    }
  }

  console.log('\n🎉 Authentication restoration complete!');
  console.log('\n📋 Next Steps:');
  console.log('1. Restart the development server');
  console.log('2. Test <NAME_EMAIL> / testpassword');
  console.log('3. Verify all dashboard functionality works with authentication');
}

restoreAuthentication().catch(console.error);
