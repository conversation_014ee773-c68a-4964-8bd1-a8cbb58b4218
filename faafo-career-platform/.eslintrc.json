{"extends": ["next", "next/core-web-vitals", "prettier", "plugin:security/recommended"], "ignorePatterns": ["src/generated/prisma/**"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn", "security/detect-object-injection": "off", "security/detect-non-literal-fs-filename": "off", "security/detect-possible-timing-attacks": "off", "@next/next/no-img-element": "warn"}}