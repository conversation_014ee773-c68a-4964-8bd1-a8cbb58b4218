const { PrismaClient } = require('./faafo-career-platform/node_modules/@prisma/client');

const prisma = new PrismaClient();

async function checkDatabaseStatus() {
  try {
    console.log('🔍 Checking database status...\n');

    // Count career paths
    const careerPathCount = await prisma.careerPath.count();
    const activeCareerPaths = await prisma.careerPath.count({
      where: { isActive: true }
    });

    console.log(`📊 Career Paths:`);
    console.log(`  Total: ${careerPathCount}`);
    console.log(`  Active: ${activeCareerPaths}`);

    // Get career path details
    const careerPaths = await prisma.careerPath.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        isActive: true,
        _count: {
          select: {
            learningResources: true,
            relatedSkills: true
          }
        }
      }
    });

    console.log('\n📋 Career Path Details:');
    careerPaths.forEach(path => {
      console.log(`  • ${path.name} (${path.isActive ? 'Active' : 'Inactive'})`);
      console.log(`    - Resources: ${path._count.learningResources}`);
      console.log(`    - Skills: ${path._count.relatedSkills}`);
    });

    // Count learning resources
    const resourceCount = await prisma.learningResource.count();
    const activeResources = await prisma.learningResource.count({
      where: { isActive: true }
    });

    console.log(`\n📚 Learning Resources:`);
    console.log(`  Total: ${resourceCount}`);
    console.log(`  Active: ${activeResources}`);

    // Get resource breakdown by category
    const resourcesByCategory = await prisma.learningResource.groupBy({
      by: ['category'],
      where: { isActive: true },
      _count: {
        category: true
      }
    });

    console.log('\n📂 Resources by Category:');
    resourcesByCategory.forEach(cat => {
      console.log(`  • ${cat.category}: ${cat._count.category} resources`);
    });

    // Get resource breakdown by type
    const resourcesByType = await prisma.learningResource.groupBy({
      by: ['type'],
      where: { isActive: true },
      _count: {
        type: true
      }
    });

    console.log('\n📝 Resources by Type:');
    resourcesByType.forEach(type => {
      console.log(`  • ${type.type}: ${type._count.type} resources`);
    });

    // Get resource breakdown by skill level
    const resourcesByLevel = await prisma.learningResource.groupBy({
      by: ['skillLevel'],
      where: { isActive: true },
      _count: {
        skillLevel: true
      }
    });

    console.log('\n🎯 Resources by Skill Level:');
    resourcesByLevel.forEach(level => {
      console.log(`  • ${level.skillLevel}: ${level._count.skillLevel} resources`);
    });

    // Check ratings
    const ratingCount = await prisma.resourceRating.count();
    console.log(`\n⭐ Resource Ratings: ${ratingCount}`);

    // Check user progress
    const progressCount = await prisma.userLearningProgress.count();
    console.log(`📈 User Progress Records: ${progressCount}`);

    // Check skills
    const skillCount = await prisma.skill.count();
    console.log(`🛠️ Skills: ${skillCount}`);

    // Check industries
    const industryCount = await prisma.industry.count();
    console.log(`🏢 Industries: ${industryCount}`);

    console.log('\n✅ Database status check complete!');

  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseStatus();
