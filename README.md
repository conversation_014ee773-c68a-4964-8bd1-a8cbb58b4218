# FAAFO Career Platform

## Overview

The FAAFO Career Platform is a comprehensive career guidance and development system that helps users discover their career paths through assessments, provides personalized recommendations, and offers a community-driven learning environment.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Getting Started](#getting-started)
- [Documentation](#documentation)
- [Development](#development)
- [Contributing](#contributing)
- [License](#license)

## Features

### Core Features
- **Career Assessments**: Comprehensive skill and interest evaluations
- **Personalized Recommendations**: AI-driven career path suggestions
- **Learning Resources**: Curated educational content and courses
- **Progress Tracking**: Goal setting and achievement monitoring
- **Community Forum**: Peer interaction and knowledge sharing

### Advanced Features
- **Achievement System**: Gamified progress recognition
- **Mentor Matching**: Connect with industry professionals
- **Resource Rating**: Community-driven content evaluation
- **Custom Learning Paths**: Personalized skill development routes

## Getting Started

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL 14+
- Git

### Quick Start
```bash
# Clone the repository
git clone https://github.com/dm601990/faafo.git
cd faafo/faafo-career-platform

# Install dependencies
npm install

# Setup environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Setup database
npx prisma migrate dev
npx prisma db seed

# Start development server
npm run dev
```

Visit `http://localhost:3000` to access the application.

## Documentation

📚 **[Complete Documentation](./docs/README.md)**

Our documentation is organized by audience and purpose:

- **[Project Management](./docs/project-management/)** - Requirements, architecture, and specifications
- **[Development](./docs/development/)** - Implementation guides and technical documentation
- **[Testing](./docs/testing/)** - Test reports and quality assurance procedures
- **[User Guides](./docs/user-guides/)** - End-user documentation and API references
- **[Operations](./docs/operations/)** - Deployment, maintenance, and operational procedures

### Quick Links
- [Project Overview](./docs/project-management/00_PROJECT_OVERVIEW.md)
- [Technical Specifications](./docs/project-management/03_TECH_SPECS.md)
- [API Documentation](./docs/user-guides/API.md)
- [Deployment Guide](./docs/operations/deployment.md)

## Development

### Technology Stack
- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **Testing**: Jest, React Testing Library, Cypress
- **Deployment**: Vercel, Docker

### Development Workflow
1. Create feature branch from `main`
2. Implement changes with tests
3. Run test suite: `npm test`
4. Submit pull request
5. Code review and approval
6. Merge to `main`

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run test suite
npm run test:watch   # Run tests in watch mode
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

## Contributing

We welcome contributions! Please see our [Development Documentation](./docs/development/) for detailed guidelines.

### How to Contribute
1. Fork the repository
2. Create a feature branch
3. Make your changes with appropriate tests
4. Ensure all tests pass
5. Submit a pull request

### Reporting Issues
- Use GitHub Issues for bug reports
- Provide detailed reproduction steps
- Include environment information

## License

This project is licensed under the MIT License - see the [LICENSE](./faafo-career-platform/LICENSE) file for details.